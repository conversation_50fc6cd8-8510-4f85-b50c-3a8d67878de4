using UnityEngine;
using UnityEditor;
using TMPro;
using UnityEngine.UI;

namespace EconomySystem.Editor
{
    /// <summary>
    /// Wizard để setup Advanced Trading Machine với trigger zones
    /// </summary>
    public class AdvancedTradingMachineWizard : EditorWindow
    {
        #region Private Fields
        private Vector2 m_ScrollPosition;
        private string m_TenMay = "Máy Giao Dịch Nâng Cấp";
        private Vector3 m_ViTri = Vector3.zero;
        private bool m_TaoModel = true;
        private bool m_TaoUI = true;
        private bool m_TaoSampleItems = true;
        private bool m_SuaLoiCurrencyUI = true;
        #endregion

        #region Menu Item
        [MenuItem("Economy System/Advanced Trading Machine Wizard", priority = 4)]
        public static void ShowWindow()
        {
            AdvancedTradingMachineWizard window = GetWindow<AdvancedTradingMachineWizard>("Advanced Trading Machine");
            window.minSize = new Vector2(450, 600);
            window.Show();
        }
        #endregion

        #region GUI
        private void OnGUI()
        {
            m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);
            
            DrawHeader();
            DrawMachineSettings();
            DrawSetupOptions();
            DrawButtons();
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.Space(10);
            
            GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
            titleStyle.fontSize = 18;
            titleStyle.alignment = TextAnchor.MiddleCenter;
            
            EditorGUILayout.LabelField("🏪 Advanced Trading Machine Wizard", titleStyle);
            EditorGUILayout.LabelField("Máy bán hàng nâng cấp với trigger zones cho objects", EditorStyles.centeredGreyMiniLabel);
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        }

        private void DrawMachineSettings()
        {
            EditorGUILayout.LabelField("⚙️ Cài Đặt Máy", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TenMay = EditorGUILayout.TextField("Tên Máy", m_TenMay);
            m_ViTri = EditorGUILayout.Vector3Field("Vị Trí Đặt Máy", m_ViTri);
            
            EditorGUILayout.Space(10);
        }

        private void DrawSetupOptions()
        {
            EditorGUILayout.LabelField("🔧 Tùy Chọn Thiết Lập", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TaoModel = EditorGUILayout.Toggle("Tạo Model 3D & Trigger Zones", m_TaoModel);
            m_TaoUI = EditorGUILayout.Toggle("Tạo UI System", m_TaoUI);
            m_TaoSampleItems = EditorGUILayout.Toggle("Tạo Sample Items & Tokens", m_TaoSampleItems);
            m_SuaLoiCurrencyUI = EditorGUILayout.Toggle("Sửa Lỗi Currency UI", m_SuaLoiCurrencyUI);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.HelpBox(
                "Advanced Trading Machine Features:\n\n" +
                "🔸 SELL ZONE (Vùng Vàng): Đặt vật phẩm vào để bán\n" +
                "🔸 BUY ZONE (Vùng Xanh): Đặt token vào để mua\n" +
                "🔸 Tự động hiện giá khi đặt object\n" +
                "🔸 Xác nhận trước khi giao dịch\n" +
                "🔸 Visual feedback và effects\n\n" +
                "Cách sử dụng:\n" +
                "1. Đặt TradableItem vào SELL ZONE → Hiện giá bán\n" +
                "2. Đặt BuyToken vào BUY ZONE → Hiện giá mua\n" +
                "3. Nhấn Xác nhận để hoàn tất giao dịch", 
                MessageType.Info);
            
            EditorGUILayout.Space(10);
        }

        private void DrawButtons()
        {
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
            EditorGUILayout.Space(10);
            
            if (GUILayout.Button("🏪 Tạo Advanced Trading Machine", GUILayout.Height(40)))
            {
                TaoAdvancedTradingMachine();
            }
            
            EditorGUILayout.Space(10);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🔧 Sửa Currency UI", GUILayout.Height(30)))
            {
                SuaLoiCurrencyUI();
            }
            
            if (GUILayout.Button("🔍 Kiểm Tra Economy", GUILayout.Height(30)))
            {
                KiemTraEconomySystem();
            }
            
            GUILayout.EndHorizontal();
        }
        #endregion

        #region Setup Methods
        private void TaoAdvancedTradingMachine()
        {
            try
            {
                EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Đang tạo máy...", 0f);
                
                // Tạo GameObject chính
                GameObject tradingMachine = new GameObject($"AdvancedTradingMachine");
                tradingMachine.transform.position = m_ViTri;
                
                // Thêm AdvancedTradingMachine component
                EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Thêm component...", 0.2f);
                AdvancedTradingMachine machine = tradingMachine.AddComponent<AdvancedTradingMachine>();
                
                // Tạo model và trigger zones
                if (m_TaoModel)
                {
                    EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Tạo model & zones...", 0.4f);
                    TaoModelVaTriggerZones(tradingMachine, machine);
                }
                
                // Tạo UI system
                if (m_TaoUI)
                {
                    EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Tạo UI system...", 0.6f);
                    TaoUISystem(tradingMachine, machine);
                }
                
                // Tạo sample items
                if (m_TaoSampleItems)
                {
                    EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Tạo sample items...", 0.8f);
                    TaoSampleItems(tradingMachine);
                }
                
                // Sửa lỗi Currency UI
                if (m_SuaLoiCurrencyUI)
                {
                    EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Sửa Currency UI...", 0.9f);
                    SuaLoiCurrencyUI();
                }
                
                EditorUtility.DisplayProgressBar("Advanced Trading Machine", "Hoàn thành!", 1f);
                
                // Select object đã tạo
                Selection.activeGameObject = tradingMachine;
                
                EditorUtility.DisplayDialog("Thành Công!", 
                    $"Đã tạo Advanced Trading Machine: {m_TenMay}\n\n" +
                    "Cách sử dụng:\n" +
                    "1. Play game\n" +
                    "2. Di chuyển Player gần máy\n" +
                    "3. Kéo SampleWood vào SELL ZONE (vàng)\n" +
                    "4. Kéo BuyToken_Stone vào BUY ZONE (xanh)\n" +
                    "5. Nhấn Xác nhận để giao dịch\n\n" +
                    "Kiểm tra Console để xem logs!", 
                    "OK");
            }
            catch (System.Exception e)
            {
                EditorUtility.DisplayDialog("Lỗi!", $"Có lỗi xảy ra: {e.Message}", "OK");
                Debug.LogError($"[AdvancedTradingMachineWizard] Lỗi: {e.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private void TaoModelVaTriggerZones(GameObject parent, AdvancedTradingMachine machine)
        {
            // Tạo model container
            GameObject modelContainer = new GameObject("Model");
            modelContainer.transform.SetParent(parent.transform, false);
            
            // Tạo base machine
            GameObject baseObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
            baseObj.name = "Base";
            baseObj.transform.SetParent(modelContainer.transform, false);
            baseObj.transform.localScale = new Vector3(3f, 2f, 2f);
            baseObj.transform.localPosition = new Vector3(0, 1f, 0);
            
            // Tạo screen
            GameObject screenObj = GameObject.CreatePrimitive(PrimitiveType.Quad);
            screenObj.name = "Screen";
            screenObj.transform.SetParent(modelContainer.transform, false);
            screenObj.transform.localPosition = new Vector3(0, 1.5f, 1.01f);
            screenObj.transform.localScale = new Vector3(2f, 1f, 1f);
            
            // Tạo Sell Zone (vùng vàng)
            GameObject sellZone = new GameObject("SellZone");
            sellZone.transform.SetParent(parent.transform, false);
            sellZone.transform.localPosition = new Vector3(-2f, 0.5f, 0);
            sellZone.transform.localScale = new Vector3(1.5f, 1f, 1.5f);
            
            BoxCollider sellCollider = sellZone.AddComponent<BoxCollider>();
            sellCollider.isTrigger = true;
            
            // Visual indicator cho sell zone
            GameObject sellIndicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            sellIndicator.name = "SellIndicator";
            sellIndicator.transform.SetParent(sellZone.transform, false);
            sellIndicator.transform.localPosition = Vector3.zero;
            sellIndicator.transform.localScale = new Vector3(1f, 0.1f, 1f);
            
            Renderer sellRenderer = sellIndicator.GetComponent<Renderer>();
            Material sellMaterial = new Material(Shader.Find("Standard"));
            sellMaterial.color = Color.yellow;
            sellMaterial.EnableKeyword("_EMISSION");
            sellMaterial.SetColor("_EmissionColor", Color.yellow * 0.3f);
            sellRenderer.sharedMaterial = sellMaterial;
            
            // Tạo Buy Zone (vùng xanh)
            GameObject buyZone = new GameObject("BuyZone");
            buyZone.transform.SetParent(parent.transform, false);
            buyZone.transform.localPosition = new Vector3(2f, 0.5f, 0);
            buyZone.transform.localScale = new Vector3(1.5f, 1f, 1.5f);
            
            BoxCollider buyCollider = buyZone.AddComponent<BoxCollider>();
            buyCollider.isTrigger = true;
            
            // Visual indicator cho buy zone
            GameObject buyIndicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            buyIndicator.name = "BuyIndicator";
            buyIndicator.transform.SetParent(buyZone.transform, false);
            buyIndicator.transform.localPosition = Vector3.zero;
            buyIndicator.transform.localScale = new Vector3(1f, 0.1f, 1f);
            
            Renderer buyRenderer = buyIndicator.GetComponent<Renderer>();
            Material buyMaterial = new Material(Shader.Find("Standard"));
            buyMaterial.color = Color.cyan;
            buyMaterial.EnableKeyword("_EMISSION");
            buyMaterial.SetColor("_EmissionColor", Color.cyan * 0.3f);
            buyRenderer.sharedMaterial = buyMaterial;
            
            // Gán references vào machine (sử dụng reflection để tránh lỗi SerializedProperty)
            var machineType = typeof(AdvancedTradingMachine);
            
            var sellZoneField = machineType.GetField("m_SellZone", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            sellZoneField?.SetValue(machine, sellZone.transform);
            
            var buyZoneField = machineType.GetField("m_BuyZone", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            buyZoneField?.SetValue(machine, buyZone.transform);
            
            var sellIndicatorField = machineType.GetField("m_IndicatorSellZone", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            sellIndicatorField?.SetValue(machine, sellIndicator);
            
            var buyIndicatorField = machineType.GetField("m_IndicatorBuyZone", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            buyIndicatorField?.SetValue(machine, buyIndicator);
            
            var sellMaterialField = machineType.GetField("m_MaterialSellZone", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            sellMaterialField?.SetValue(machine, sellMaterial);
            
            var buyMaterialField = machineType.GetField("m_MaterialBuyZone", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            buyMaterialField?.SetValue(machine, buyMaterial);
            
            Debug.Log("[AdvancedTradingMachineWizard] Đã tạo model và trigger zones");
        }

        private void TaoUISystem(GameObject parent, AdvancedTradingMachine machine)
        {
            // Tạo Canvas
            GameObject canvasObj = new GameObject("TradingUI_Canvas");
            canvasObj.transform.SetParent(parent.transform, false);
            canvasObj.transform.localPosition = new Vector3(0, 3f, 0);
            
            Canvas canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.WorldSpace;
            canvas.worldCamera = Camera.main;
            
            CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPhysicalSize;
            
            canvasObj.AddComponent<GraphicRaycaster>();
            
            RectTransform canvasRect = canvas.GetComponent<RectTransform>();
            canvasRect.sizeDelta = new Vector2(600, 400);
            
            // Tạo các panels cơ bản (sẽ được setup đầy đủ trong game)
            TaoPromptPanel(canvasObj);
            TaoSellPanel(canvasObj);
            TaoBuyPanel(canvasObj);
            
            // Gán canvas vào machine
            var machineType = typeof(AdvancedTradingMachine);
            var canvasField = machineType.GetField("m_UICanvas", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            canvasField?.SetValue(machine, canvas);
            
            Debug.Log("[AdvancedTradingMachineWizard] Đã tạo UI system");
        }

        private void TaoPromptPanel(GameObject canvasParent)
        {
            GameObject promptPanel = new GameObject("PromptPanel");
            promptPanel.transform.SetParent(canvasParent.transform, false);
            
            RectTransform promptRect = promptPanel.AddComponent<RectTransform>();
            promptRect.anchorMin = Vector2.zero;
            promptRect.anchorMax = Vector2.one;
            promptRect.offsetMin = Vector2.zero;
            promptRect.offsetMax = Vector2.zero;
            
            Image promptBg = promptPanel.AddComponent<Image>();
            promptBg.color = new Color(0, 0, 0, 0.7f);
            
            GameObject promptText = new GameObject("PromptText");
            promptText.transform.SetParent(promptPanel.transform, false);
            
            TextMeshProUGUI textComp = promptText.AddComponent<TextMeshProUGUI>();
            textComp.text = "🏪 Advanced Trading Machine\n📦 Đặt vật phẩm vào vùng VÀNG để BÁN\n🛒 Đặt token vào vùng XANH để MUA";
            textComp.fontSize = 24;
            textComp.color = Color.white;
            textComp.alignment = TextAlignmentOptions.Center;
            
            RectTransform textRect = promptText.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
        }

        private void TaoSellPanel(GameObject canvasParent)
        {
            GameObject sellPanel = new GameObject("SellPanel");
            sellPanel.transform.SetParent(canvasParent.transform, false);
            sellPanel.SetActive(false);
            
            // Setup cơ bản - sẽ được hoàn thiện trong runtime
            RectTransform sellRect = sellPanel.AddComponent<RectTransform>();
            sellRect.anchorMin = Vector2.zero;
            sellRect.anchorMax = Vector2.one;
            sellRect.offsetMin = Vector2.zero;
            sellRect.offsetMax = Vector2.zero;
            
            Image sellBg = sellPanel.AddComponent<Image>();
            sellBg.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);
        }

        private void TaoBuyPanel(GameObject canvasParent)
        {
            GameObject buyPanel = new GameObject("BuyPanel");
            buyPanel.transform.SetParent(canvasParent.transform, false);
            buyPanel.SetActive(false);
            
            // Setup cơ bản - sẽ được hoàn thiện trong runtime
            RectTransform buyRect = buyPanel.AddComponent<RectTransform>();
            buyRect.anchorMin = Vector2.zero;
            buyRect.anchorMax = Vector2.one;
            buyRect.offsetMin = Vector2.zero;
            buyRect.offsetMax = Vector2.zero;
            
            Image buyBg = buyPanel.AddComponent<Image>();
            buyBg.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);
        }

        private void TaoSampleItems(GameObject parent)
        {
            // Tạo sample tradable item (wood)
            GameObject sampleWood = GameObject.CreatePrimitive(PrimitiveType.Cube);
            sampleWood.name = "SampleWood";
            sampleWood.transform.position = parent.transform.position + new Vector3(-4f, 1f, 0);
            sampleWood.transform.localScale = Vector3.one * 0.5f;
            
            TradableItem woodItem = sampleWood.AddComponent<TradableItem>();
            woodItem.ItemId = "wood";
            woodItem.ItemName = "Gỗ";
            woodItem.BasePrice = 10;
            
            Renderer woodRenderer = sampleWood.GetComponent<Renderer>();
            Material woodMaterial = new Material(Shader.Find("Standard"));
            woodMaterial.color = new Color(0.6f, 0.3f, 0.1f); // Màu nâu
            woodRenderer.sharedMaterial = woodMaterial;
            
            // Thêm Rigidbody để có thể kéo thả
            Rigidbody woodRb = sampleWood.AddComponent<Rigidbody>();
            woodRb.mass = 0.5f;
            
            // Tạo buy token (stone)
            GameObject buyTokenStone = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            buyTokenStone.name = "BuyToken_Stone";
            buyTokenStone.transform.position = parent.transform.position + new Vector3(4f, 1f, 0);
            buyTokenStone.transform.localScale = Vector3.one * 0.3f;
            
            BuyToken stoneToken = buyTokenStone.AddComponent<BuyToken>();
            stoneToken.ItemId = "stone";
            stoneToken.ItemName = "Đá";
            
            Renderer stoneRenderer = buyTokenStone.GetComponent<Renderer>();
            Material stoneMaterial = new Material(Shader.Find("Standard"));
            stoneMaterial.color = Color.gray;
            stoneRenderer.sharedMaterial = stoneMaterial;
            
            // Thêm Rigidbody
            Rigidbody stoneRb = buyTokenStone.AddComponent<Rigidbody>();
            stoneRb.mass = 0.3f;
            
            Debug.Log("[AdvancedTradingMachineWizard] Đã tạo sample items");
        }

        private void SuaLoiCurrencyUI()
        {
            // Tìm tất cả Currency UI trong scene
            LeaCurrencyUI[] currencyUIs = FindObjectsOfType<LeaCurrencyUI>();
            
            foreach (var currencyUI in currencyUIs)
            {
                // Thêm CurrencyUIFixer
                CurrencyUIFixer fixer = currencyUI.GetComponent<CurrencyUIFixer>();
                if (fixer == null)
                {
                    fixer = currencyUI.gameObject.AddComponent<CurrencyUIFixer>();
                }
                
                // Sửa lỗi tự động
                fixer.SuaLoiTuDong();
            }
            
            Debug.Log($"[AdvancedTradingMachineWizard] Đã sửa lỗi cho {currencyUIs.Length} Currency UI");
            
            if (currencyUIs.Length == 0)
            {
                Debug.LogWarning("[AdvancedTradingMachineWizard] Không tìm thấy Currency UI nào để sửa!");
            }
        }

        private void KiemTraEconomySystem()
        {
            EconomySystemManager economy = FindObjectOfType<EconomySystemManager>();
            string report = "=== KIỂM TRA ECONOMY SYSTEM ===\n\n";
            
            if (economy != null)
            {
                report += "✅ EconomySystemManager: Có\n";
                report += $"✅ Trạng thái: {(economy.HeThongSanSang ? "Sẵn sàng" : "Chưa sẵn sàng")}\n";
                
                if (economy.CurrencyManager != null)
                    report += $"✅ Currency: {economy.CurrencyManager.DinhDangTienHienTai()}\n";
                
                if (economy.PlayerInventory != null)
                    report += $"✅ Inventory: {economy.PlayerInventory.SoSlotDaDung} slots đã dùng\n";
            }
            else
            {
                report += "❌ EconomySystemManager: Không tìm thấy!\n";
                report += "Hãy chạy Economy Setup Wizard trước.";
            }
            
            // Kiểm tra Currency UI
            LeaCurrencyUI[] currencyUIs = FindObjectsOfType<LeaCurrencyUI>();
            report += $"\n💰 Currency UIs: {currencyUIs.Length}\n";
            
            if (currencyUIs.Length == 0)
            {
                report += "⚠️ Không tìm thấy Currency UI!\n";
            }
            
            EditorUtility.DisplayDialog("Báo Cáo Economy System", report, "OK");
        }
        #endregion
    }
}
