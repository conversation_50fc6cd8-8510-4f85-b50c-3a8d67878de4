using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;

namespace EconomySystem
{
    /// <summary>
    /// Quản lý kho hàng của người chơi
    /// Hỗ trợ thêm, xó<PERSON>, sắp xếp vật phẩm và auto-save
    /// </summary>
    public class PlayerInventory : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎒 Cài Đặt Kho Hàng")]
        [SerializeField] private int m_SoSlotToiDa = 50;
        [SerializeField] private bool m_TuDongSapXep = true;
        [SerializeField] private bool m_TuDongLuuKho = true;
        [SerializeField] private bool m_HienThiLog = true;

        [Header("💾 Cài Đặt Lưu Trữ")]
        [SerializeField] private string m_KeyLuuTru = "PlayerInventory";
        [SerializeField] private float m_KhoangThoiGianLuu = 60f;

        [Header("📦 Danh Sách Vật Phẩm")]
        [SerializeField] private List<InventoryItem> m_DanhSachVatPham = new List<InventoryItem>();
        #endregion

        #region Events
        public static event Action<InventoryItem> OnItemAdded;
        public static event Action<InventoryItem> OnItemRemoved;
        public static event Action<InventoryItem, int> OnItemQuantityChanged;
        public static event Action OnInventoryChanged;
        public static event Action<string> OnInventoryFull;
        #endregion

        #region Properties
        public List<InventoryItem> DanhSachVatPham => new List<InventoryItem>(m_DanhSachVatPham);
        public int SoSlotDaDung => m_DanhSachVatPham.Count;
        public int SoSlotConLai => m_SoSlotToiDa - SoSlotDaDung;
        public bool KhoDay => SoSlotDaDung >= m_SoSlotToiDa;
        public float PhanTramDay => (float)SoSlotDaDung / m_SoSlotToiDa;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TaiDuLieu();
        }

        private void Start()
        {
            if (m_TuDongLuuKho)
            {
                InvokeRepeating(nameof(LuuDuLieu), m_KhoangThoiGianLuu, m_KhoangThoiGianLuu);
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && m_TuDongLuuKho)
                LuuDuLieu();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && m_TuDongLuuKho)
                LuuDuLieu();
        }

        private void OnDestroy()
        {
            if (m_TuDongLuuKho)
                LuuDuLieu();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thêm vật phẩm vào kho
        /// </summary>
        public bool ThemVatPham(string itemId, int soLuong = 1)
        {
            if (string.IsNullOrEmpty(itemId) || soLuong <= 0)
            {
                LogLoi($"Thông tin vật phẩm không hợp lệ: {itemId}, số lượng: {soLuong}");
                return false;
            }

            // Tìm vật phẩm đã có trong kho
            var vatPhamCoSan = m_DanhSachVatPham.FirstOrDefault(item => item.ItemId == itemId);
            
            if (vatPhamCoSan != null)
            {
                // Cập nhật số lượng
                vatPhamCoSan.SoLuong += soLuong;
                Log($"Cập nhật {itemId}: +{soLuong} (Tổng: {vatPhamCoSan.SoLuong})");
                OnItemQuantityChanged?.Invoke(vatPhamCoSan, vatPhamCoSan.SoLuong);
            }
            else
            {
                // Kiểm tra kho đầy
                if (KhoDay)
                {
                    string thongBao = "Kho hàng đã đầy!";
                    LogLoi(thongBao);
                    OnInventoryFull?.Invoke(thongBao);
                    return false;
                }

                // Thêm vật phẩm mới
                var vatPhamMoi = new InventoryItem(itemId, soLuong);
                m_DanhSachVatPham.Add(vatPhamMoi);
                Log($"Thêm vật phẩm mới: {itemId} x{soLuong}");
                OnItemAdded?.Invoke(vatPhamMoi);
            }

            if (m_TuDongSapXep)
                SapXepKho();

            OnInventoryChanged?.Invoke();
            return true;
        }

        /// <summary>
        /// Xóa vật phẩm khỏi kho
        /// </summary>
        public bool XoaVatPham(string itemId, int soLuong = 1)
        {
            var vatPham = m_DanhSachVatPham.FirstOrDefault(item => item.ItemId == itemId);
            
            if (vatPham == null)
            {
                LogLoi($"Không tìm thấy vật phẩm: {itemId}");
                return false;
            }

            if (vatPham.SoLuong < soLuong)
            {
                LogLoi($"Không đủ số lượng! Cần: {soLuong}, Có: {vatPham.SoLuong}");
                return false;
            }

            vatPham.SoLuong -= soLuong;
            Log($"Xóa {itemId}: -{soLuong} (Còn: {vatPham.SoLuong})");

            if (vatPham.SoLuong <= 0)
            {
                m_DanhSachVatPham.Remove(vatPham);
                Log($"Đã xóa hoàn toàn: {itemId}");
                OnItemRemoved?.Invoke(vatPham);
            }
            else
            {
                OnItemQuantityChanged?.Invoke(vatPham, vatPham.SoLuong);
            }

            OnInventoryChanged?.Invoke();
            return true;
        }

        /// <summary>
        /// Kiểm tra có vật phẩm trong kho
        /// </summary>
        public bool CoVatPham(string itemId, int soLuongCanThiet = 1)
        {
            var vatPham = m_DanhSachVatPham.FirstOrDefault(item => item.ItemId == itemId);
            return vatPham != null && vatPham.SoLuong >= soLuongCanThiet;
        }

        /// <summary>
        /// Lấy số lượng vật phẩm
        /// </summary>
        public int LaySoLuongVatPham(string itemId)
        {
            var vatPham = m_DanhSachVatPham.FirstOrDefault(item => item.ItemId == itemId);
            return vatPham?.SoLuong ?? 0;
        }

        /// <summary>
        /// Sắp xếp kho hàng theo tên
        /// </summary>
        public void SapXepKho()
        {
            m_DanhSachVatPham.Sort((a, b) => string.Compare(a.ItemId, b.ItemId));
            Log("Đã sắp xếp kho hàng");
            OnInventoryChanged?.Invoke();
        }

        /// <summary>
        /// Xóa toàn bộ kho hàng
        /// </summary>
        public void XoaToanBoKho()
        {
            m_DanhSachVatPham.Clear();
            Log("Đã xóa toàn bộ kho hàng");
            OnInventoryChanged?.Invoke();
        }
        #endregion

        #region Data Management
        private void TaiDuLieu()
        {
            string duLieu = PlayerPrefs.GetString(m_KeyLuuTru, "");
            if (!string.IsNullOrEmpty(duLieu))
            {
                try
                {
                    var inventoryData = JsonUtility.FromJson<InventoryData>(duLieu);
                    m_DanhSachVatPham = inventoryData.items ?? new List<InventoryItem>();
                    Log($"Tải dữ liệu kho: {m_DanhSachVatPham.Count} vật phẩm");
                }
                catch (Exception e)
                {
                    LogLoi($"Lỗi tải dữ liệu: {e.Message}");
                    m_DanhSachVatPham = new List<InventoryItem>();
                }
            }
        }

        private void LuuDuLieu()
        {
            try
            {
                var inventoryData = new InventoryData { items = m_DanhSachVatPham };
                string duLieu = JsonUtility.ToJson(inventoryData);
                PlayerPrefs.SetString(m_KeyLuuTru, duLieu);
                PlayerPrefs.Save();
                Log($"Lưu dữ liệu kho: {m_DanhSachVatPham.Count} vật phẩm");
            }
            catch (Exception e)
            {
                LogLoi($"Lỗi lưu dữ liệu: {e.Message}");
            }
        }

        public void XoaDuLieu()
        {
            PlayerPrefs.DeleteKey(m_KeyLuuTru);
            m_DanhSachVatPham.Clear();
            Log("Xóa dữ liệu kho hàng");
            OnInventoryChanged?.Invoke();
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[PlayerInventory] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[PlayerInventory] {message}");
        }

        public List<InventoryItem> TimKiemVatPham(string tuKhoa)
        {
            return m_DanhSachVatPham.Where(item => 
                item.ItemId.ToLower().Contains(tuKhoa.ToLower())).ToList();
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Thêm Vật Phẩm Test")]
        private void ThemVatPhamTest()
        {
            ThemVatPham("wood", 5);
            ThemVatPham("stone", 3);
            ThemVatPham("sword", 1);
        }

        [ContextMenu("Xóa Vật Phẩm Test")]
        private void XoaVatPhamTest()
        {
            XoaVatPham("wood", 2);
        }

        [ContextMenu("Hiển Thị Thông Tin Kho")]
        private void HienThiThongTinKho()
        {
            string thongTin = $"=== THÔNG TIN KHO HÀNG ===\n" +
                             $"Số slot đã dùng: {SoSlotDaDung}/{m_SoSlotToiDa}\n" +
                             $"Phần trăm đầy: {PhanTramDay:P1}\n" +
                             $"Danh sách vật phẩm:\n";
            
            foreach (var item in m_DanhSachVatPham)
            {
                thongTin += $"- {item.ItemId}: {item.SoLuong}\n";
            }
            
            Debug.Log(thongTin);
        }
        #endif
        #endregion
    }

    /// <summary>
    /// Cấu trúc dữ liệu vật phẩm trong kho
    /// </summary>
    [System.Serializable]
    public class InventoryItem
    {
        public string ItemId;
        public int SoLuong;
        public DateTime NgayThem;

        public InventoryItem(string itemId, int soLuong)
        {
            ItemId = itemId;
            SoLuong = soLuong;
            NgayThem = DateTime.Now;
        }
    }

    /// <summary>
    /// Cấu trúc dữ liệu để lưu trữ
    /// </summary>
    [System.Serializable]
    public class InventoryData
    {
        public List<InventoryItem> items;
    }
}
