using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace EconomySystem
{
    /// <summary>
    /// Phiên bản đơn giản của Trading Machine để test nhanh
    /// Không cần UI phức tạp, chỉ cần trigger zone và basic functionality
    /// </summary>
    public class SimpleTradingMachine : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🏪 Cài Đặt Máy")]
        [SerializeField] private string m_TenMay = "Máy Giao Dịch";
        [SerializeField] private string m_ItemId = "wood";
        [SerializeField] private int m_GiaMua = 10;      // Gi<PERSON> máy mua từ người chơi
        [SerializeField] private int m_GiaBan = 15;      // Giá máy bán cho người chơi
        [SerializeField] private bool m_ChoPhepMua = true;
        [SerializeField] private bool m_ChoPhepBan = true;
        [SerializeField] private bool m_ChoPhepChinhGia = true;

        [Header("🎯 Trigger Zone")]
        [SerializeField] private LayerMask m_PlayerLayer = -1;
        [SerializeField] private float m_KhoangCachTuongTac = 3f;

        [Header("🔧 Settings")]
        [SerializeField] private bool m_HienThiLog = true;
        [SerializeField] private KeyCode m_PhimTuongTac = KeyCode.F;
        [SerializeField] private KeyCode m_PhimChinhGia = KeyCode.G;
        #endregion

        #region Private Fields
        private EconomySystemManager m_EconomySystem;
        private bool m_PlayerTrongZone = false;
        private bool m_UIOpen = false;
        private int m_SoLuongGiaoDich = 1;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            TimEconomySystem();
            ThietLapTriggerZone();
        }

        private void Update()
        {
            XuLyInput();
        }

        private void OnTriggerEnter(Collider other)
        {
            if (KiemTraPlayer(other))
            {
                m_PlayerTrongZone = true;
                HienThiPrompt(true);
                Log($"Player vào zone: {m_TenMay}");
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (KiemTraPlayer(other))
            {
                m_PlayerTrongZone = false;
                HienThiPrompt(false);
                m_UIOpen = false;
                Log($"Player rời zone: {m_TenMay}");
            }
        }

        private void OnGUI()
        {
            if (m_UIOpen && m_PlayerTrongZone)
            {
                HienThiGUI();
            }
            else if (m_PlayerTrongZone)
            {
                HienThiPromptGUI();
            }
        }
        #endregion

        #region Setup Methods
        private void TimEconomySystem()
        {
            m_EconomySystem = FindObjectOfType<EconomySystemManager>();
            if (m_EconomySystem == null)
            {
                LogLoi("Không tìm thấy EconomySystemManager!");
            }
        }

        private void ThietLapTriggerZone()
        {
            Collider col = GetComponent<Collider>();
            if (col == null)
            {
                col = gameObject.AddComponent<BoxCollider>();
                Log("Đã thêm BoxCollider tự động");
            }
            col.isTrigger = true;
        }

        private bool KiemTraPlayer(Collider other)
        {
            return ((1 << other.gameObject.layer) & m_PlayerLayer) != 0;
        }
        #endregion

        #region Input Handling
        private void XuLyInput()
        {
            if (m_PlayerTrongZone)
            {
                if (Input.GetKeyDown(m_PhimTuongTac))
                {
                    m_UIOpen = !m_UIOpen;
                    Log($"UI: {(m_UIOpen ? "Mở" : "Đóng")}");
                }

                if (Input.GetKeyDown(m_PhimChinhGia) && m_ChoPhepChinhGia)
                {
                    ChinhGiaNhanh();
                }

                if (m_UIOpen)
                {
                    // Điều chỉnh số lượng
                    if (Input.GetKeyDown(KeyCode.Plus) || Input.GetKeyDown(KeyCode.KeypadPlus))
                    {
                        m_SoLuongGiaoDich = Mathf.Min(m_SoLuongGiaoDich + 1, 99);
                    }
                    if (Input.GetKeyDown(KeyCode.Minus) || Input.GetKeyDown(KeyCode.KeypadMinus))
                    {
                        m_SoLuongGiaoDich = Mathf.Max(m_SoLuongGiaoDich - 1, 1);
                    }

                    // Mua/bán
                    if (Input.GetKeyDown(KeyCode.B) && m_ChoPhepMua)
                    {
                        XuLyMua();
                    }
                    if (Input.GetKeyDown(KeyCode.S) && m_ChoPhepBan)
                    {
                        XuLyBan();
                    }
                }
            }

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                m_UIOpen = false;
            }
        }
        #endregion

        #region GUI Methods
        private void HienThiPromptGUI()
        {
            GUI.Box(new Rect(Screen.width / 2 - 150, Screen.height - 100, 300, 50), 
                   $"Nhấn [{m_PhimTuongTac}] để tương tác với {m_TenMay}");
        }

        private void HienThiGUI()
        {
            // Main window
            GUILayout.BeginArea(new Rect(Screen.width / 2 - 200, Screen.height / 2 - 150, 400, 300));
            GUILayout.BeginVertical("box");

            // Header
            GUILayout.Label($"🏪 {m_TenMay}", GUI.skin.GetStyle("label"));
            GUILayout.Label($"Vật phẩm: {m_ItemId}");
            
            if (m_EconomySystem != null)
            {
                GUILayout.Label($"Tiền hiện tại: {m_EconomySystem.CurrencyManager.DinhDangTienHienTai()}");
                
                if (m_EconomySystem.PlayerInventory != null)
                {
                    int soLuongCoSan = m_EconomySystem.PlayerInventory.LaySoLuongVatPham(m_ItemId);
                    GUILayout.Label($"Trong kho: {soLuongCoSan} {m_ItemId}");
                }
            }

            GUILayout.Space(10);

            // Price info
            GUILayout.Label($"💰 Giá mua (máy mua từ bạn): {m_GiaMua} Lea");
            GUILayout.Label($"💰 Giá bán (máy bán cho bạn): {m_GiaBan} Lea");

            GUILayout.Space(10);

            // Quantity control
            GUILayout.BeginHorizontal();
            GUILayout.Label($"Số lượng: {m_SoLuongGiaoDich}");
            if (GUILayout.Button("-"))
            {
                m_SoLuongGiaoDich = Mathf.Max(m_SoLuongGiaoDich - 1, 1);
            }
            if (GUILayout.Button("+"))
            {
                m_SoLuongGiaoDich = Mathf.Min(m_SoLuongGiaoDich + 1, 99);
            }
            GUILayout.EndHorizontal();

            // Total prices
            int tongTienMua = m_GiaMua * m_SoLuongGiaoDich;
            int tongTienBan = m_GiaBan * m_SoLuongGiaoDich;
            GUILayout.Label($"Tổng tiền nhận (bán): {tongTienMua} Lea");
            GUILayout.Label($"Tổng tiền trả (mua): {tongTienBan} Lea");

            GUILayout.Space(10);

            // Action buttons
            GUILayout.BeginHorizontal();
            
            GUI.enabled = m_ChoPhepBan && CoTheThucHienGiaoDich(false);
            if (GUILayout.Button($"Bán {m_SoLuongGiaoDich} {m_ItemId} [S]"))
            {
                XuLyBan();
            }
            
            GUI.enabled = m_ChoPhepMua && CoTheThucHienGiaoDich(true);
            if (GUILayout.Button($"Mua {m_SoLuongGiaoDich} {m_ItemId} [B]"))
            {
                XuLyMua();
            }
            
            GUI.enabled = true;
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            // Control info
            GUILayout.Label("Điều khiển:");
            GUILayout.Label($"[{m_PhimTuongTac}] Đóng UI | [+/-] Số lượng | [B] Mua | [S] Bán");
            if (m_ChoPhepChinhGia)
            {
                GUILayout.Label($"[{m_PhimChinhGia}] Chỉnh giá nhanh");
            }

            if (GUILayout.Button("Đóng"))
            {
                m_UIOpen = false;
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private void HienThiPrompt(bool hienThi)
        {
            // Có thể thêm UI prompt ở đây nếu cần
        }
        #endregion

        #region Trading Operations
        private bool CoTheThucHienGiaoDich(bool laMua)
        {
            if (m_EconomySystem == null) return false;

            if (laMua)
            {
                // Kiểm tra mua
                int tongTien = m_GiaBan * m_SoLuongGiaoDich;
                bool duTien = m_EconomySystem.CurrencyManager.CoTheMua(tongTien);
                bool khoKhongDay = !m_EconomySystem.PlayerInventory.KhoDay || 
                                  m_EconomySystem.PlayerInventory.CoVatPham(m_ItemId);
                return duTien && khoKhongDay;
            }
            else
            {
                // Kiểm tra bán
                return m_EconomySystem.PlayerInventory.CoVatPham(m_ItemId, m_SoLuongGiaoDich);
            }
        }

        private void XuLyMua()
        {
            if (m_EconomySystem == null) return;

            if (m_EconomySystem.MuaVatPham(m_ItemId, m_SoLuongGiaoDich, m_GiaBan))
            {
                int tongTien = m_GiaBan * m_SoLuongGiaoDich;
                Log($"Mua thành công: {m_SoLuongGiaoDich} {m_ItemId} với {tongTien} Lea");
            }
            else
            {
                LogLoi("Không thể mua vật phẩm!");
            }
        }

        private void XuLyBan()
        {
            if (m_EconomySystem == null) return;

            if (m_EconomySystem.BanVatPham(m_ItemId, m_SoLuongGiaoDich, m_GiaMua))
            {
                int tongTien = m_GiaMua * m_SoLuongGiaoDich;
                Log($"Bán thành công: {m_SoLuongGiaoDich} {m_ItemId} nhận {tongTien} Lea");
            }
            else
            {
                LogLoi("Không thể bán vật phẩm!");
            }
        }

        private void ChinhGiaNhanh()
        {
            // Tăng giá 10%
            m_GiaMua = Mathf.RoundToInt(m_GiaMua * 1.1f);
            m_GiaBan = Mathf.RoundToInt(m_GiaBan * 1.1f);
            Log($"Chỉnh giá: Mua={m_GiaMua}, Bán={m_GiaBan}");
        }
        #endregion

        #region Public Methods
        public void DatGia(int giaMua, int giaBan)
        {
            m_GiaMua = Mathf.Max(1, giaMua);
            m_GiaBan = Mathf.Max(1, giaBan);
            Log($"Đặt giá: Mua={m_GiaMua}, Bán={m_GiaBan}");
        }

        public void DatItemId(string itemId)
        {
            m_ItemId = itemId;
            Log($"Đặt item: {m_ItemId}");
        }

        public void BatTatChucNang(bool choPhepMua, bool choPhepBan)
        {
            m_ChoPhepMua = choPhepMua;
            m_ChoPhepBan = choPhepBan;
            Log($"Chức năng: Mua={m_ChoPhepMua}, Bán={m_ChoPhepBan}");
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[SimpleTradingMachine-{m_TenMay}] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[SimpleTradingMachine-{m_TenMay}] {message}");
        }

        public string LayThongTinMay()
        {
            string thongTin = $"=== {m_TenMay} ===\n";
            thongTin += $"Item: {m_ItemId}\n";
            thongTin += $"Giá mua: {m_GiaMua} Lea\n";
            thongTin += $"Giá bán: {m_GiaBan} Lea\n";
            thongTin += $"Cho phép mua: {m_ChoPhepMua}\n";
            thongTin += $"Cho phép bán: {m_ChoPhepBan}\n";
            thongTin += $"Player trong zone: {m_PlayerTrongZone}\n";
            thongTin += $"UI mở: {m_UIOpen}";
            return thongTin;
        }
        #endregion

        #region Gizmos
        private void OnDrawGizmos()
        {
            // Vẽ trigger zone
            Gizmos.color = m_PlayerTrongZone ? Color.green : Color.yellow;
            Gizmos.DrawWireCube(transform.position, Vector3.one * m_KhoangCachTuongTac * 2);
            
            // Vẽ label
            UnityEditor.Handles.Label(transform.position + Vector3.up * 2, m_TenMay);
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Test Mở UI")]
        private void TestMoUI()
        {
            m_UIOpen = true;
            m_PlayerTrongZone = true;
        }

        [ContextMenu("Test Đóng UI")]
        private void TestDongUI()
        {
            m_UIOpen = false;
        }

        [ContextMenu("Hiển Thị Thông Tin")]
        private void HienThiThongTin()
        {
            Debug.Log(LayThongTinMay());
        }

        [ContextMenu("Chỉnh Giá +10%")]
        private void ChinhGiaTang()
        {
            ChinhGiaNhanh();
        }
        #endif
        #endregion
    }
}
