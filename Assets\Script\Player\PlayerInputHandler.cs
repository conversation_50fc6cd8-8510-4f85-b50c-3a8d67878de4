using UnityEngine;
using UnityEngine.InputSystem;
using System;

namespace PlayerSystem
{
    /// <summary>
    /// Xử lý tất cả input cho Player System
    /// Tích hợp với Unity Input System và cung cấp events cho các components khác
    /// </summary>
    public class PlayerInputHandler : MonoBehaviour
    {
        #region Events
        /// <summary>Event khi có input di chuyển</summary>
        public event Action<Vector2> OnMoveInput;
        
        /// <summary>Event khi có input look</summary>
        public event Action<Vector2> OnLookInput;
        
        /// <summary>Event khi nhấn jump</summary>
        public event Action OnJumpPressed;
        
        /// <summary>Event khi thả jump</summary>
        public event Action OnJumpReleased;
        
        /// <summary>Event khi thay đổi sprint state</summary>
        public event Action<bool> OnSprintChanged;
        
        /// <summary>Event khi nhấn crouch</summary>
        public event Action OnCrouchPressed;
        
        /// <summary>Event khi nhấn toggle camera</summary>
        public event Action OnCameraTogglePressed;
        
        /// <summary>Event khi nhấn interact</summary>
        public event Action OnInteractPressed;
        
        /// <summary>Event khi nhấn attack</summary>
        public event Action OnAttackPressed;
        #endregion

        #region Private Fields
        [Header("Input Settings")]
        [SerializeField, Tooltip("Có bật input không")]
        private bool m_InputEnabled = true;
        
        [SerializeField, Tooltip("Deadzone cho analog sticks")]
        private float m_AnalogDeadzone = 0.1f;
        
        [SerializeField, Tooltip("Multiplier cho gamepad sensitivity")]
        private float m_GamepadSensitivityMultiplier = 1.5f;

        [Header("Debug")]
        [SerializeField, Tooltip("Hiển thị debug logs")]
        private bool m_ShowDebugLogs = false;

        // Input values
        private Vector2 m_CurrentMoveInput;
        private Vector2 m_CurrentLookInput;
        private bool m_IsSprintPressed;
        private bool m_IsJumpPressed;
        private PlayerInput m_PlayerInput;
        private InputActionMap m_PlayerActionMap;
        #endregion

        #region Public Properties
        /// <summary>Input di chuyển hiện tại</summary>
        public Vector2 CurrentMoveInput => m_CurrentMoveInput;
        
        /// <summary>Input look hiện tại</summary>
        public Vector2 CurrentLookInput => m_CurrentLookInput;
        
        /// <summary>Có đang nhấn sprint không</summary>
        public bool IsSprintPressed => m_IsSprintPressed;
        
        /// <summary>Có đang nhấn jump không</summary>
        public bool IsJumpPressed => m_IsJumpPressed;
        
        /// <summary>Có input được bật không</summary>
        public bool InputEnabled => m_InputEnabled;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeInput();
        }

        private void OnEnable()
        {
            EnableInput();
        }

        private void OnDisable()
        {
            DisableInput();
        }

        private void Update()
        {
            ProcessContinuousInput();
        }
        #endregion

        #region Input Callbacks
        /// <summary>Callback cho Move action từ Input System</summary>
        public void OnMove(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            Vector2 input = _context.ReadValue<Vector2>();
            
            // Áp dụng deadzone
            if (input.magnitude < m_AnalogDeadzone)
            {
                input = Vector2.zero;
            }
            
            m_CurrentMoveInput = input;
            OnMoveInput?.Invoke(m_CurrentMoveInput);
            
            if (m_ShowDebugLogs)
            {
                Debug.Log($"Move Input: {m_CurrentMoveInput}");
            }
        }

        /// <summary>Callback cho Look action từ Input System</summary>
        public void OnLook(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;

            Vector2 input = _context.ReadValue<Vector2>();

            // Điều chỉnh sensitivity cho gamepad
            if (_context.control.device is Gamepad)
            {
                input *= m_GamepadSensitivityMultiplier;
            }
            else
            {
                // Apply frame rate independent scaling for mouse
                input = ApplyFrameRateIndependentScaling(input);
            }

            m_CurrentLookInput = input;
            OnLookInput?.Invoke(m_CurrentLookInput);
        }

        /// <summary>Legacy callback cho Look - để tương thích với Input System Send Messages</summary>
        public void OnLook(Vector2 _lookInput)
        {
            if (!m_InputEnabled) return;

            m_CurrentLookInput = _lookInput;
            OnLookInput?.Invoke(m_CurrentLookInput);

            if (m_ShowDebugLogs)
            {
                Debug.Log($"OnLook(Vector2) called with: {_lookInput}");
            }
        }

        /// <summary>Alternative signature cho Input System - thử các signature khác nhau</summary>
        public void OnLook(float x, float y)
        {
            OnLook(new Vector2(x, y));
        }

        /// <summary>Alternative signature cho Input System - với InputValue</summary>
        public void OnLook(UnityEngine.InputSystem.InputValue _value)
        {
            Vector2 lookInput = _value.Get<Vector2>();
            OnLook(lookInput);
        }

        /// <summary>Legacy callback cho Move - để tương thích với Input System Send Messages</summary>
        public void OnMove(Vector2 _moveInput)
        {
            if (!m_InputEnabled) return;

            // Áp dụng deadzone
            if (_moveInput.magnitude < m_AnalogDeadzone)
            {
                _moveInput = Vector2.zero;
            }

            m_CurrentMoveInput = _moveInput;
            OnMoveInput?.Invoke(m_CurrentMoveInput);

            if (m_ShowDebugLogs)
            {
                Debug.Log($"OnMove(Vector2) called with: {m_CurrentMoveInput}");
            }
        }

        /// <summary>Alternative signature cho Move với InputValue</summary>
        public void OnMove(UnityEngine.InputSystem.InputValue _value)
        {
            Vector2 moveInput = _value.Get<Vector2>();
            OnMove(moveInput);
        }

        /// <summary>Callback cho Jump action từ Input System</summary>
        public void OnJump(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            if (_context.performed)
            {
                m_IsJumpPressed = true;
                OnJumpPressed?.Invoke();
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log("Jump Pressed");
                }
            }
            else if (_context.canceled)
            {
                m_IsJumpPressed = false;
                OnJumpReleased?.Invoke();
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log("Jump Released");
                }
            }
        }

        /// <summary>Callback cho Sprint action từ Input System</summary>
        public void OnSprint(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            bool wasSprintPressed = m_IsSprintPressed;
            m_IsSprintPressed = _context.ReadValue<float>() > 0.5f;
            
            if (wasSprintPressed != m_IsSprintPressed)
            {
                OnSprintChanged?.Invoke(m_IsSprintPressed);
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log($"Sprint: {(m_IsSprintPressed ? "Started" : "Stopped")}");
                }
            }
        }

        /// <summary>Callback cho Crouch action từ Input System</summary>
        public void OnCrouch(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            if (_context.performed)
            {
                OnCrouchPressed?.Invoke();
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log("Crouch Pressed");
                }
            }
        }

        /// <summary>Callback cho ToggleCamera action từ Input System</summary>
        public void OnToggleCamera(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            if (_context.performed)
            {
                OnCameraTogglePressed?.Invoke();
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log("Camera Toggle Pressed");
                }
            }
        }

        /// <summary>Callback cho Interact action từ Input System</summary>
        public void OnInteract(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            if (_context.performed)
            {
                OnInteractPressed?.Invoke();
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log("Interact Pressed");
                }
            }
        }

        /// <summary>Callback cho Attack action từ Input System</summary>
        public void OnAttack(InputAction.CallbackContext _context)
        {
            if (!m_InputEnabled) return;
            
            if (_context.performed)
            {
                OnAttackPressed?.Invoke();
                
                if (m_ShowDebugLogs)
                {
                    Debug.Log("Attack Pressed");
                }
            }
        }

        /// <summary>Legacy callbacks cho các input khác - để tương thích với Input System Send Messages</summary>
        public void OnJump(bool _pressed)
        {
            if (!m_InputEnabled) return;

            if (_pressed && !m_IsJumpPressed)
            {
                m_IsJumpPressed = true;
                OnJumpPressed?.Invoke();

                if (m_ShowDebugLogs)
                {
                    Debug.Log("OnJump(bool) - Jump Pressed");
                }
            }
            else if (!_pressed && m_IsJumpPressed)
            {
                m_IsJumpPressed = false;
                OnJumpReleased?.Invoke();

                if (m_ShowDebugLogs)
                {
                    Debug.Log("OnJump(bool) - Jump Released");
                }
            }
        }

        /// <summary>Alternative signature cho Jump với InputValue</summary>
        public void OnJump(UnityEngine.InputSystem.InputValue _value)
        {
            bool pressed = _value.isPressed;
            OnJump(pressed);
        }

        /// <summary>Alternative signature cho Jump - no parameters</summary>
        public void OnJump()
        {
            if (!m_InputEnabled) return;

            OnJumpPressed?.Invoke();

            if (m_ShowDebugLogs)
            {
                Debug.Log("OnJump() - Jump Triggered");
            }
        }

        public void OnSprint(bool _pressed)
        {
            if (!m_InputEnabled) return;

            bool wasSprintPressed = m_IsSprintPressed;
            m_IsSprintPressed = _pressed;

            if (wasSprintPressed != m_IsSprintPressed)
            {
                OnSprintChanged?.Invoke(m_IsSprintPressed);

                if (m_ShowDebugLogs)
                {
                    Debug.Log($"OnSprint(bool) - Sprint: {(m_IsSprintPressed ? "Started" : "Stopped")}");
                }
            }
        }

        /// <summary>Alternative signature cho Sprint với InputValue</summary>
        public void OnSprint(UnityEngine.InputSystem.InputValue _value)
        {
            bool pressed = _value.isPressed;
            OnSprint(pressed);
        }

        /// <summary>Alternative signature cho Sprint với float</summary>
        public void OnSprint(float _value)
        {
            bool pressed = _value > 0.5f;
            OnSprint(pressed);
        }

        public void OnCrouch()
        {
            if (!m_InputEnabled) return;

            OnCrouchPressed?.Invoke();

            if (m_ShowDebugLogs)
            {
                Debug.Log("OnCrouch() - Crouch Pressed");
            }
        }

        /// <summary>Alternative signature cho Crouch với InputValue</summary>
        public void OnCrouch(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed)
            {
                OnCrouch();
            }
        }

        public void OnToggleCamera()
        {
            if (!m_InputEnabled) return;

            OnCameraTogglePressed?.Invoke();

            if (m_ShowDebugLogs)
            {
                Debug.Log("OnToggleCamera() - Camera Toggle Pressed");
            }
        }

        /// <summary>Alternative signature cho ToggleCamera với InputValue</summary>
        public void OnToggleCamera(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed)
            {
                OnToggleCamera();
            }
        }

        public void OnInteract()
        {
            if (!m_InputEnabled) return;

            OnInteractPressed?.Invoke();

            if (m_ShowDebugLogs)
            {
                Debug.Log("OnInteract() - Interact Pressed");
            }
        }

        /// <summary>Alternative signature cho Interact với InputValue</summary>
        public void OnInteract(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed)
            {
                OnInteract();
            }
        }

        public void OnAttack()
        {
            if (!m_InputEnabled) return;

            OnAttackPressed?.Invoke();

            if (m_ShowDebugLogs)
            {
                Debug.Log("OnAttack() - Attack Pressed");
            }
        }

        /// <summary>Alternative signature cho Attack với InputValue</summary>
        public void OnAttack(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed)
            {
                OnAttack();
            }
        }
        #endregion

        #region Private Methods
        private void InitializeInput()
        {
            m_PlayerInput = GetComponent<PlayerInput>();
            
            if (m_PlayerInput == null)
            {
                Debug.LogError("PlayerInputHandler: PlayerInput component không tìm thấy!", this);
                return;
            }
            
            // Lấy Player action map
            m_PlayerActionMap = m_PlayerInput.actions.FindActionMap("Player");
            
            if (m_PlayerActionMap == null)
            {
                Debug.LogError("PlayerInputHandler: Player action map không tìm thấy!", this);
            }
        }

        private void EnableInput()
        {
            if (m_PlayerActionMap != null)
            {
                m_PlayerActionMap.Enable();
            }
        }

        private void DisableInput()
        {
            if (m_PlayerActionMap != null)
            {
                m_PlayerActionMap.Disable();
            }
        }

        private void ProcessContinuousInput()
        {
            // Xử lý input liên tục nếu cần
            // Ví dụ: smooth input transitions, input buffering, etc.
        }

        /// <summary>Apply frame rate independent scaling for mouse input</summary>
        private Vector2 ApplyFrameRateIndependentScaling(Vector2 _input)
        {
            // Scale mouse input to be frame rate independent
            // This helps maintain consistent sensitivity across different frame rates
            float targetFrameRate = 60f;
            float currentFrameRate = 1f / Time.unscaledDeltaTime;
            float scaleFactor = targetFrameRate / Mathf.Max(currentFrameRate, 30f); // Clamp minimum to 30 FPS

            return _input * Mathf.Clamp(scaleFactor, 0.5f, 2f); // Clamp scale factor to reasonable range
        }
        #endregion

        #region Public Methods
        /// <summary>Bật/tắt input</summary>
        public void SetInputEnabled(bool _enabled)
        {
            m_InputEnabled = _enabled;
            
            if (_enabled)
            {
                EnableInput();
            }
            else
            {
                DisableInput();
                // Reset input values
                m_CurrentMoveInput = Vector2.zero;
                m_CurrentLookInput = Vector2.zero;
                m_IsSprintPressed = false;
                m_IsJumpPressed = false;
            }
        }

        /// <summary>Thiết lập deadzone cho analog input</summary>
        public void SetAnalogDeadzone(float _deadzone)
        {
            m_AnalogDeadzone = Mathf.Clamp01(_deadzone);
        }

        /// <summary>Thiết lập gamepad sensitivity multiplier</summary>
        public void SetGamepadSensitivityMultiplier(float _multiplier)
        {
            m_GamepadSensitivityMultiplier = Mathf.Max(0.1f, _multiplier);
        }

        /// <summary>Lấy input device hiện tại</summary>
        public InputDevice GetCurrentInputDevice()
        {
            return m_PlayerInput?.devices.Count > 0 ? m_PlayerInput.devices[0] : null;
        }

        /// <summary>Kiểm tra xem có đang sử dụng gamepad không</summary>
        public bool IsUsingGamepad()
        {
            return GetCurrentInputDevice() is Gamepad;
        }

        /// <summary>Kiểm tra xem có đang sử dụng keyboard & mouse không</summary>
        public bool IsUsingKeyboardMouse()
        {
            var device = GetCurrentInputDevice();
            return device is Keyboard || device is Mouse;
        }
        #endregion

        #if UNITY_EDITOR
        [ContextMenu("Log Current Input State")]
        private void LogCurrentInputState()
        {
            Debug.Log($"PlayerInputHandler State:\n" +
                     $"Move: {m_CurrentMoveInput}\n" +
                     $"Look: {m_CurrentLookInput}\n" +
                     $"Sprint: {m_IsSprintPressed}\n" +
                     $"Jump: {m_IsJumpPressed}\n" +
                     $"Input Enabled: {m_InputEnabled}\n" +
                     $"Current Device: {GetCurrentInputDevice()?.name ?? "None"}");
        }

        [ContextMenu("Toggle Debug Logs")]
        private void ToggleDebugLogs()
        {
            m_ShowDebugLogs = !m_ShowDebugLogs;
            Debug.Log($"PlayerInputHandler: Debug logs {(m_ShowDebugLogs ? "enabled" : "disabled")}");
        }
        #endif
    }
}
