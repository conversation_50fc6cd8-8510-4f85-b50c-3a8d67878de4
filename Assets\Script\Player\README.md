# 🎮 Player System - Unity Character Controller

## 📋 Tổng Quan

Hệ thống điều khiển nhân vật hoàn chỉnh cho Unity với CharacterController, hỗ trợ First/Third Person view và tích hợp với Economy System hiện có.

## 🗂️ Cấu Trúc Files

```
Assets/Script/Player/
├── PlayerController.cs          # Script chính điều khiển nhân vật
├── CameraController.cs          # Hệ thống camera First/Third Person
├── PlayerInputHandler.cs        # Xử lý input từ Unity Input System
├── PlayerMovement.cs           # Logic movement và physics
├── PlayerSettings.cs           # ScriptableObject cho settings
├── PlayerSetupUtility.cs       # Utility để auto-setup Player
├── Editor/
│   └── PlayerSystemEditor.cs   # Editor tools và menu items
└── README.md                   # File này
```

## ✨ Tính Năng Chính

### 🏃 Movement System
- **CharacterController-based**: Sử dụng CharacterController thay vì Rigidbody
- **Walk/Sprint/Crouch**: Ba chế độ di chuyển với tốc độ khác nhau
- **Single Jump**: Mộ<PERSON> lần nhảy per attempt với faster falling speed
- **Air Control**: Điều khiển nhẹ khi đang trong không khí
- **Ground Check**: Kiểm tra chính xác khi nào có thể nhảy

### 📷 Camera System (🆕 Improved!)
- **Dual Mode**: Chuyển đổi mượt mà giữa First Person và Third Person
- **Advanced Mouse Look**: Điều khiển camera với mouse/gamepad với input smoothing
- **Multiple Interpolation**: SmoothDamp, Lerp, Slerp cho camera rotation
- **Frame Rate Independence**: Sensitivity nhất quán ở mọi FPS
- **High DPI Support**: Tự động detect và điều chỉnh cho màn hình high DPI
- **Mouse Acceleration**: Hỗ trợ mouse acceleration curve
- **Collision Detection**: Camera tự động điều chỉnh khi va chạm
- **Smooth Transitions**: Chuyển đổi mượt mà giữa các chế độ

### 🎯 Input System
- **Unity Input System**: Tích hợp hoàn toàn với Unity Input System
- **Multi-Device Support**: Hỗ trợ keyboard, mouse, gamepad
- **Event-Driven**: Sử dụng events để tách biệt input logic
- **Configurable**: Deadzone và sensitivity có thể điều chỉnh

### 🔧 Inspector Friendly
- **Edit Mode Access**: Tất cả settings có thể điều chỉnh trong Edit mode
- **Organized Layout**: Sử dụng [Header], [Range], [Tooltip]
- **ScriptableObject Settings**: PlayerSettings cho multiple presets
- **Debug Tools**: Built-in debug visualization và logging

## 🚀 Quick Start

### Cách 1: Sử dụng Menu Tools (Khuyến nghị)

1. **Tạo Player tự động:**
   ```
   Tools → Player System → Create Complete Player
   ```

2. **Kiểm tra setup:**
   ```
   Tools → Player System → Validate Player Setup
   ```

### Cách 2: Manual Setup

1. **Tạo Player GameObject:**
   ```
   GameObject → Create Empty → "Player"
   ```

2. **Thêm Components:**
   ```
   Add Component → Character Controller
   Add Component → Player Input
   Add Component → Player Input Handler
   Add Component → Player Controller
   ```

3. **Setup Camera:**
   ```
   Tools → Player System → Setup Selected GameObject as Player
   ```

## ⚙️ Configuration

### Movement Settings
```csharp
Walk Speed: 5f          // Tốc độ đi bộ
Sprint Speed: 8f        // Tốc độ chạy
Crouch Speed: 2f        // Tốc độ cúi người
Jump Height: 2f         // Độ cao nhảy
Gravity: -15f           // Lực trọng lực
```

### Camera Settings (🆕 Enhanced!)
```csharp
// Basic Settings
Mouse Sensitivity: 100f         // Độ nhạy chuột
Third Person Distance: 5f       // Khoảng cách camera
Smooth Camera: true             // Camera mượt mà

// Advanced Settings (New!)
Interpolation Type: SmoothDamp  // None/SmoothDamp/Lerp/Slerp
Interpolation Speed: 0.15f      // Tốc độ cho Lerp/Slerp
Enable Input Smoothing: true    // Làm mượt input
Input Smooth Time: 0.05f        // Thời gian smooth input
Mouse Acceleration: 1f          // Hệ số acceleration
High DPI Multiplier: 1f         // Multiplier cho high DPI
```

### Input Bindings
```
WASD/Arrow Keys: Movement
Mouse: Look Around
Space: Jump
Shift: Sprint
C: Crouch
V: Toggle Camera
```

## 🔗 Economy System Integration

Player System được thiết kế để tích hợp với Economy System hiện có:

1. **Không Conflict**: Input và raycast không conflict với Economy
2. **Compatible**: Hoạt động với EconomyPlayerAdapter
3. **Extensible**: Dễ dàng extend cho thêm features

### Setup Integration
```csharp
// Thêm EconomyPlayerAdapter vào Player
Player GameObject → Add Component → Economy Player Adapter

// Cấu hình references
Player Controller: [Auto-assigned]
Camera Controller: [Auto-assigned]
```

## 🛠️ API Reference

### PlayerController
```csharp
// Properties
bool IsGrounded { get; }
bool IsSprinting { get; }
bool IsCrouching { get; }
float CurrentSpeed { get; }

// Methods
Vector2 GetMoveInput()
bool CanJump()
void SetCameraController(CameraController controller)
```

### CameraController
```csharp
// Properties
bool IsFirstPerson { get; }
Camera PlayerCamera { get; }

// Methods
void ToggleCameraMode()
void SetCameraMode(bool isFirstPerson)
void SetMouseSensitivity(float sensitivity)
Vector3 GetCameraForward()
```

### PlayerInputHandler
```csharp
// Events
event Action<Vector2> OnMoveInput
event Action OnJumpPressed
event Action<bool> OnSprintChanged
event Action OnCrouchPressed

// Properties
Vector2 CurrentMoveInput { get; }
bool IsSprintPressed { get; }
bool InputEnabled { get; }
```

## 🔧 Debug Tools

### Menu Tools
```
Tools → Player System → Create Complete Player
Tools → Player System → Validate Player Setup
Tools → Player System → Debug → Log All Players
```

### Context Menu
```
Right-click PlayerController → Quick Setup
Right-click PlayerController → Validate Setup
Right-click CameraController → Test Camera Toggle
```

### Runtime Debug
- **Gizmos**: Ground check, movement direction, camera position
- **Console Logs**: Input events, state changes
- **Inspector Info**: Real-time movement data

## 📝 Best Practices

### Performance
1. Sử dụng LayerMask để giới hạn collision checks
2. Điều chỉnh Ground Check Distance phù hợp
3. Disable input khi không cần thiết

### Setup
1. Luôn validate setup sau khi tạo Player
2. Sử dụng PlayerSettings cho multiple presets
3. Test cả keyboard và gamepad input

### Integration
1. Kiểm tra compatibility với existing systems
2. Sử dụng events thay vì direct coupling
3. Document custom modifications

## 🐛 Troubleshooting

### Player không di chuyển
- Kiểm tra CharacterController component
- Verify Input System setup
- Check Ground layer và colliders

### Camera không hoạt động
- Kiểm tra Camera hierarchy
- Verify CameraController references
- Check Input bindings

### Economy Integration issues
- Ensure EconomyPlayerAdapter is added
- Check for input conflicts
- Verify raycast layers

## 📚 Documentation

- `PlayerControllerGuide.md` - Hướng dẫn chi tiết và đầy đủ
- `CAMERA_IMPROVEMENTS_GUIDE.md` - 🆕 Hướng dẫn camera improvements mới

## 🎯 Recent Updates

### ✅ Camera Improvements (Latest)
- ✅ Input smoothing và filtering
- ✅ Frame rate independence
- ✅ Multiple interpolation methods
- ✅ Mouse acceleration support
- ✅ High DPI display support
- ✅ Advanced camera settings

## 🎯 Future Enhancements

- [ ] Swimming mechanics
- [ ] Climbing system
- [ ] Advanced camera effects
- [ ] Custom movement modes
- [ ] Multiplayer support

---

**Developed with ❤️ following Unity C# Best Practices**
