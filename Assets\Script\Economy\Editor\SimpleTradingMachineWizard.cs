using UnityEngine;
using UnityEditor;

namespace EconomySystem.Editor
{
    /// <summary>
    /// Wizard đơn giản để tạo Simple Trading Machine
    /// Không có lỗi SerializedProperty phức tạp
    /// </summary>
    public class SimpleTradingMachineWizard : EditorWindow
    {
        #region Private Fields
        private Vector2 m_ScrollPosition;
        private string m_TenMay = "Máy Giao Dịch Gỗ";
        private string m_ItemId = "wood";
        private int m_GiaMua = 10;
        private int m_GiaBan = 15;
        private bool m_ChoPhepMua = true;
        private bool m_ChoPhepBan = true;
        private bool m_TaoModel = true;
        private Vector3 m_ViTri = Vector3.zero;
        private Vector3 m_KichThuocZone = new Vector3(6f, 4f, 6f);
        #endregion

        #region Menu Item
        [MenuItem("Economy System/Simple Trading Machine Wizard", priority = 3)]
        public static void ShowWindow()
        {
            SimpleTradingMachineWizard window = GetWindow<SimpleTradingMachineWizard>("Simple Trading Machine");
            window.minSize = new Vector2(400, 500);
            window.Show();
        }
        #endregion

        #region GUI
        private void OnGUI()
        {
            m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);
            
            DrawHeader();
            DrawMachineSettings();
            DrawSetupOptions();
            DrawButtons();
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.Space(10);
            
            GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
            titleStyle.fontSize = 18;
            titleStyle.alignment = TextAnchor.MiddleCenter;
            
            EditorGUILayout.LabelField("🏪 Simple Trading Machine Wizard", titleStyle);
            EditorGUILayout.LabelField("Tạo máy mua/bán đơn giản với OnGUI", EditorStyles.centeredGreyMiniLabel);
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        }

        private void DrawMachineSettings()
        {
            EditorGUILayout.LabelField("⚙️ Cài Đặt Máy", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TenMay = EditorGUILayout.TextField("Tên Máy", m_TenMay);
            m_ItemId = EditorGUILayout.TextField("Item ID", m_ItemId);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("💰 Cài Đặt Giá", EditorStyles.boldLabel);
            m_GiaMua = EditorGUILayout.IntField("Giá Mua (Máy mua từ player)", m_GiaMua);
            m_GiaBan = EditorGUILayout.IntField("Giá Bán (Máy bán cho player)", m_GiaBan);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("🔧 Chức Năng", EditorStyles.boldLabel);
            m_ChoPhepMua = EditorGUILayout.Toggle("Cho Phép Mua", m_ChoPhepMua);
            m_ChoPhepBan = EditorGUILayout.Toggle("Cho Phép Bán", m_ChoPhepBan);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("📍 Vị Trí & Kích Thước", EditorStyles.boldLabel);
            m_ViTri = EditorGUILayout.Vector3Field("Vị Trí Đặt Máy", m_ViTri);
            m_KichThuocZone = EditorGUILayout.Vector3Field("Kích Thước Trigger Zone", m_KichThuocZone);
            
            EditorGUILayout.Space(10);
        }

        private void DrawSetupOptions()
        {
            EditorGUILayout.LabelField("🔧 Tùy Chọn Thiết Lập", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TaoModel = EditorGUILayout.Toggle("Tạo Model 3D Đơn Giản", m_TaoModel);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.HelpBox(
                "Simple Trading Machine sử dụng OnGUI thay vì UI Canvas phức tạp.\n" +
                "Điều khiển:\n" +
                "• F: Mở/đóng UI\n" +
                "• G: Chỉnh giá nhanh (+10%)\n" +
                "• B: Mua | S: Bán\n" +
                "• +/-: Điều chỉnh số lượng", 
                MessageType.Info);
            
            EditorGUILayout.Space(10);
        }

        private void DrawButtons()
        {
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
            EditorGUILayout.Space(10);
            
            if (GUILayout.Button("🏪 Tạo Simple Trading Machine", GUILayout.Height(40)))
            {
                TaoSimpleTradingMachine();
            }
            
            EditorGUILayout.Space(10);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🔍 Kiểm Tra Economy", GUILayout.Height(30)))
            {
                KiemTraEconomySystem();
            }
            
            if (GUILayout.Button("🎯 Tìm Trading Machines", GUILayout.Height(30)))
            {
                TimTradingMachines();
            }
            
            GUILayout.EndHorizontal();
        }
        #endregion

        #region Setup Methods
        private void TaoSimpleTradingMachine()
        {
            try
            {
                EditorUtility.DisplayProgressBar("Simple Trading Machine", "Đang tạo máy...", 0f);
                
                // Tạo GameObject chính
                GameObject tradingMachine = new GameObject($"SimpleTradingMachine_{m_ItemId}");
                tradingMachine.transform.position = m_ViTri;
                
                // Thêm SimpleTradingMachine component
                EditorUtility.DisplayProgressBar("Simple Trading Machine", "Thêm component...", 0.3f);
                SimpleTradingMachine machine = tradingMachine.AddComponent<SimpleTradingMachine>();
                
                // Cấu hình máy (trực tiếp set giá trị, không dùng SerializedProperty)
                EditorUtility.DisplayProgressBar("Simple Trading Machine", "Cấu hình máy...", 0.5f);
                CauHinhMayTrucTiep(machine);
                
                // Thêm BoxCollider trigger
                EditorUtility.DisplayProgressBar("Simple Trading Machine", "Thiết lập trigger zone...", 0.7f);
                ThietLapTriggerZone(tradingMachine);
                
                // Tạo model đơn giản
                if (m_TaoModel)
                {
                    EditorUtility.DisplayProgressBar("Simple Trading Machine", "Tạo model...", 0.9f);
                    TaoModelDonGian(tradingMachine);
                }
                
                EditorUtility.DisplayProgressBar("Simple Trading Machine", "Hoàn thành!", 1f);
                
                // Select object đã tạo
                Selection.activeGameObject = tradingMachine;
                
                EditorUtility.DisplayDialog("Thành Công!", 
                    $"Đã tạo Simple Trading Machine: {m_TenMay}\n\n" +
                    $"Item: {m_ItemId}\n" +
                    $"Giá mua: {m_GiaMua} Lea\n" +
                    $"Giá bán: {m_GiaBan} Lea\n" +
                    $"Chức năng: {(m_ChoPhepMua ? "Mua " : "")}{(m_ChoPhepBan ? "Bán" : "")}\n\n" +
                    "Cách sử dụng:\n" +
                    "1. Play game\n" +
                    "2. Di chuyển Player vào trigger zone (hình hộp vàng)\n" +
                    "3. Nhấn F để mở UI\n" +
                    "4. Sử dụng B (mua), S (bán), +/- (số lượng)", 
                    "OK");
            }
            catch (System.Exception e)
            {
                EditorUtility.DisplayDialog("Lỗi!", $"Có lỗi xảy ra: {e.Message}", "OK");
                Debug.LogError($"[SimpleTradingMachineWizard] Lỗi: {e.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private void CauHinhMayTrucTiep(SimpleTradingMachine machine)
        {
            // Sử dụng reflection để set private fields
            var type = typeof(SimpleTradingMachine);
            
            // Set tên máy
            var tenMayField = type.GetField("m_TenMay", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            tenMayField?.SetValue(machine, m_TenMay);
            
            // Set item ID
            var itemIdField = type.GetField("m_ItemId", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            itemIdField?.SetValue(machine, m_ItemId);
            
            // Set giá
            var giaMuaField = type.GetField("m_GiaMua", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            giaMuaField?.SetValue(machine, m_GiaMua);
            
            var giaBanField = type.GetField("m_GiaBan", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            giaBanField?.SetValue(machine, m_GiaBan);
            
            // Set chức năng
            var choPhepMuaField = type.GetField("m_ChoPhepMua", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            choPhepMuaField?.SetValue(machine, m_ChoPhepMua);
            
            var choPhepBanField = type.GetField("m_ChoPhepBan", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            choPhepBanField?.SetValue(machine, m_ChoPhepBan);
            
            // Set log
            var hienThiLogField = type.GetField("m_HienThiLog", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            hienThiLogField?.SetValue(machine, true);
            
            Debug.Log("[SimpleTradingMachineWizard] Đã cấu hình máy bằng reflection");
        }

        private void ThietLapTriggerZone(GameObject tradingMachine)
        {
            BoxCollider triggerCol = tradingMachine.AddComponent<BoxCollider>();
            triggerCol.isTrigger = true;
            triggerCol.size = m_KichThuocZone;
            triggerCol.center = new Vector3(0, m_KichThuocZone.y / 2, 0);
            
            Debug.Log("[SimpleTradingMachineWizard] Đã thiết lập trigger zone");
        }

        private void TaoModelDonGian(GameObject parent)
        {
            // Tạo container cho model
            GameObject modelContainer = new GameObject("Model");
            modelContainer.transform.SetParent(parent.transform, false);
            
            // Tạo base (thân máy) - màu xám
            GameObject baseObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
            baseObj.name = "Base";
            baseObj.transform.SetParent(modelContainer.transform, false);
            baseObj.transform.localScale = new Vector3(1.5f, 2f, 0.8f);
            baseObj.transform.localPosition = new Vector3(0, 1f, 0);
            
            Renderer baseRenderer = baseObj.GetComponent<Renderer>();
            Material baseMaterial = new Material(Shader.Find("Standard"));
            baseMaterial.color = Color.gray;
            baseRenderer.sharedMaterial = baseMaterial;
            
            // Tạo screen (màn hình) - màu xanh đậm
            GameObject screenObj = GameObject.CreatePrimitive(PrimitiveType.Quad);
            screenObj.name = "Screen";
            screenObj.transform.SetParent(modelContainer.transform, false);
            screenObj.transform.localPosition = new Vector3(0, 1.5f, 0.41f);
            screenObj.transform.localScale = new Vector3(1f, 0.7f, 1f);
            
            Renderer screenRenderer = screenObj.GetComponent<Renderer>();
            Material screenMaterial = new Material(Shader.Find("Standard"));
            screenMaterial.color = new Color(0.1f, 0.3f, 0.8f); // Xanh đậm
            screenMaterial.EnableKeyword("_EMISSION");
            screenMaterial.SetColor("_EmissionColor", new Color(0.1f, 0.3f, 0.8f) * 0.5f);
            screenRenderer.sharedMaterial = screenMaterial;
            
            // Tạo indicator dựa trên chức năng
            TaoIndicatorsDonGian(modelContainer);
            
            // Tạo zone indicator (vùng tương tác)
            GameObject zoneIndicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            zoneIndicator.name = "ZoneIndicator";
            zoneIndicator.transform.SetParent(modelContainer.transform, false);
            zoneIndicator.transform.localPosition = new Vector3(0, 0.05f, 0);
            zoneIndicator.transform.localScale = new Vector3(m_KichThuocZone.x, 0.1f, m_KichThuocZone.z);
            
            Renderer zoneRenderer = zoneIndicator.GetComponent<Renderer>();
            Material zoneMaterial = new Material(Shader.Find("Standard"));
            zoneMaterial.color = new Color(1f, 1f, 0f, 0.3f); // Vàng trong suốt
            zoneMaterial.SetFloat("_Mode", 3); // Transparent mode
            zoneMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            zoneMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            zoneMaterial.SetInt("_ZWrite", 0);
            zoneMaterial.DisableKeyword("_ALPHATEST_ON");
            zoneMaterial.EnableKeyword("_ALPHABLEND_ON");
            zoneMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            zoneMaterial.renderQueue = 3000;
            zoneRenderer.sharedMaterial = zoneMaterial;
            
            Debug.Log("[SimpleTradingMachineWizard] Đã tạo model đơn giản");
        }

        private void TaoIndicatorsDonGian(GameObject parent)
        {
            float yPos = 2.5f;
            
            // Buy indicator (xanh lá) - nếu cho phép mua
            if (m_ChoPhepMua)
            {
                GameObject buyIndicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                buyIndicator.name = "BuyIndicator";
                buyIndicator.transform.SetParent(parent.transform, false);
                buyIndicator.transform.localPosition = new Vector3(-0.5f, yPos, 0);
                buyIndicator.transform.localScale = Vector3.one * 0.2f;
                
                Renderer buyRenderer = buyIndicator.GetComponent<Renderer>();
                Material buyMaterial = new Material(Shader.Find("Standard"));
                buyMaterial.color = Color.green;
                buyMaterial.EnableKeyword("_EMISSION");
                buyMaterial.SetColor("_EmissionColor", Color.green * 0.3f);
                buyRenderer.sharedMaterial = buyMaterial;
            }
            
            // Sell indicator (vàng) - nếu cho phép bán
            if (m_ChoPhepBan)
            {
                GameObject sellIndicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                sellIndicator.name = "SellIndicator";
                sellIndicator.transform.SetParent(parent.transform, false);
                sellIndicator.transform.localPosition = new Vector3(0.5f, yPos, 0);
                sellIndicator.transform.localScale = Vector3.one * 0.2f;
                
                Renderer sellRenderer = sellIndicator.GetComponent<Renderer>();
                Material sellMaterial = new Material(Shader.Find("Standard"));
                sellMaterial.color = Color.yellow;
                sellMaterial.EnableKeyword("_EMISSION");
                sellMaterial.SetColor("_EmissionColor", Color.yellow * 0.3f);
                sellRenderer.sharedMaterial = sellMaterial;
            }
        }

        private void KiemTraEconomySystem()
        {
            EconomySystemManager economy = FindObjectOfType<EconomySystemManager>();
            string report = "=== KIỂM TRA ECONOMY SYSTEM ===\n\n";
            
            if (economy != null)
            {
                report += "✅ EconomySystemManager: Có\n";
                report += $"✅ Trạng thái: {(economy.HeThongSanSang ? "Sẵn sàng" : "Chưa sẵn sàng")}\n";
                
                if (economy.CurrencyManager != null)
                    report += $"✅ Currency: {economy.CurrencyManager.DinhDangTienHienTai()}\n";
                
                if (economy.PlayerInventory != null)
                    report += $"✅ Inventory: {economy.PlayerInventory.SoSlotDaDung} slots đã dùng\n";
            }
            else
            {
                report += "❌ EconomySystemManager: Không tìm thấy!\n";
                report += "Hãy chạy Economy Setup Wizard trước.";
            }
            
            EditorUtility.DisplayDialog("Báo Cáo Economy System", report, "OK");
        }

        private void TimTradingMachines()
        {
            SimpleTradingMachine[] simpleMachines = FindObjectsOfType<SimpleTradingMachine>();
            TradingMachine[] complexMachines = FindObjectsOfType<TradingMachine>();
            
            string report = $"=== TRADING MACHINES ===\n\n";
            report += $"Simple Trading Machines: {simpleMachines.Length}\n";
            report += $"Complex Trading Machines: {complexMachines.Length}\n\n";
            
            if (simpleMachines.Length > 0)
            {
                report += "Simple Machines:\n";
                foreach (var machine in simpleMachines)
                {
                    report += $"🏪 {machine.name}\n";
                }
            }
            
            if (complexMachines.Length > 0)
            {
                report += "\nComplex Machines:\n";
                foreach (var machine in complexMachines)
                {
                    report += $"🏪 {machine.name}\n";
                }
            }
            
            if (simpleMachines.Length == 0 && complexMachines.Length == 0)
            {
                report += "Không tìm thấy Trading Machine nào.";
            }
            
            EditorUtility.DisplayDialog("Trading Machines", report, "OK");
            
            // Select tất cả machines
            var allMachines = new GameObject[simpleMachines.Length + complexMachines.Length];
            for (int i = 0; i < simpleMachines.Length; i++)
                allMachines[i] = simpleMachines[i].gameObject;
            for (int i = 0; i < complexMachines.Length; i++)
                allMachines[simpleMachines.Length + i] = complexMachines[i].gameObject;
            
            if (allMachines.Length > 0)
                Selection.objects = allMachines;
        }
        #endregion
    }
}
