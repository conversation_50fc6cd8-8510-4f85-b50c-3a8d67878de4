using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace PlayerSystem
{
    /// <summary>
    /// Helper script để test và debug camera rotation improvements
    /// Hi<PERSON>n thị thông tin real-time về camera performance
    /// </summary>
    public class CameraTestHelper : MonoBehaviour
    {
        #region Private Fields
        [Header("UI References")]
        [Serial<PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>("Text để hiển thị FPS")]
        private TextMeshProUGUI m_FPSText;
        
        [Serial<PERSON><PERSON><PERSON>, Tooltip("Text để hiển thị camera info")]
        private TextMeshProUGUI m_CameraInfoText;
        
        [SerializeField, Toolt<PERSON>("Text để hiển thị input info")]
        private TextMeshProUGUI m_InputInfoText;
        
        [Header("Settings")]
        [SerializeField, Toolt<PERSON>("Có hiển thị debug info không")]
        private bool m_ShowDebugInfo = true;
        
        [SerializeField, Range(0.1f, 2f), Tooltip("Tần suất cập nhật UI (giây)")]
        private float m_UpdateInterval = 0.2f;

        // Private variables
        private CameraController m_CameraController;
        private PlayerInputHandler m_InputHandler;
        private float m_LastUpdateTime;
        private float m_FrameCount;
        private float m_FPS;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            InitializeComponents();
            SetupUI();
        }

        private void Update()
        {
            if (!m_ShowDebugInfo) return;
            
            UpdateFPS();
            
            if (Time.time - m_LastUpdateTime >= m_UpdateInterval)
            {
                UpdateUI();
                m_LastUpdateTime = Time.time;
            }
        }
        #endregion

        #region Private Methods
        private void InitializeComponents()
        {
            // Tìm CameraController
            m_CameraController = FindObjectOfType<CameraController>();
            if (m_CameraController == null)
            {
                Debug.LogWarning("CameraTestHelper: CameraController không tìm thấy!");
            }

            // Tìm PlayerInputHandler
            m_InputHandler = FindObjectOfType<PlayerInputHandler>();
            if (m_InputHandler == null)
            {
                Debug.LogWarning("CameraTestHelper: PlayerInputHandler không tìm thấy!");
            }
        }

        private void SetupUI()
        {
            // Auto-create UI nếu không có
            if (m_FPSText == null || m_CameraInfoText == null || m_InputInfoText == null)
            {
                CreateDebugUI();
            }
        }

        private void CreateDebugUI()
        {
            // Tạo Canvas nếu chưa có
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasGO = new GameObject("Debug Canvas");
                canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<CanvasScaler>();
                canvasGO.AddComponent<GraphicRaycaster>();
            }

            // Tạo FPS Text
            if (m_FPSText == null)
            {
                m_FPSText = CreateDebugText("FPS Text", canvas.transform, new Vector2(-200, 200));
            }

            // Tạo Camera Info Text
            if (m_CameraInfoText == null)
            {
                m_CameraInfoText = CreateDebugText("Camera Info", canvas.transform, new Vector2(-200, 100));
            }

            // Tạo Input Info Text
            if (m_InputInfoText == null)
            {
                m_InputInfoText = CreateDebugText("Input Info", canvas.transform, new Vector2(-200, 0));
            }
        }

        private TextMeshProUGUI CreateDebugText(string _name, Transform _parent, Vector2 _position)
        {
            GameObject textGO = new GameObject(_name);
            textGO.transform.SetParent(_parent);
            
            RectTransform rectTransform = textGO.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = _position;
            rectTransform.sizeDelta = new Vector2(400, 100);
            
            TextMeshProUGUI text = textGO.AddComponent<TextMeshProUGUI>();
            text.text = _name;
            text.fontSize = 14;
            text.color = Color.white;
            text.alignment = TextAlignmentOptions.TopLeft;
            
            return text;
        }

        private void UpdateFPS()
        {
            m_FrameCount++;
            
            if (Time.time - m_LastUpdateTime >= 1f)
            {
                m_FPS = m_FrameCount / (Time.time - m_LastUpdateTime);
                m_FrameCount = 0;
            }
        }

        private void UpdateUI()
        {
            if (m_FPSText != null)
            {
                m_FPSText.text = $"FPS: {m_FPS:F1}\nFrame Time: {Time.deltaTime * 1000:F1}ms";
            }

            if (m_CameraInfoText != null && m_CameraController != null)
            {
                Vector2 rotation = m_CameraController.CurrentRotation;
                string cameraMode = m_CameraController.IsFirstPerson ? "First Person" : "Third Person";
                
                m_CameraInfoText.text = $"Camera Mode: {cameraMode}\n" +
                                       $"Rotation: X={rotation.x:F1}°, Y={rotation.y:F1}°\n" +
                                       $"Camera Pos: {m_CameraController.PlayerCamera.transform.position}";
            }

            if (m_InputInfoText != null && m_InputHandler != null)
            {
                string inputDevice = m_InputHandler.IsUsingGamepad() ? "Gamepad" : "Keyboard/Mouse";
                
                m_InputInfoText.text = $"Input Device: {inputDevice}\n" +
                                      $"Input Enabled: {m_InputHandler.enabled}\n" +
                                      $"Current Device: {m_InputHandler.GetCurrentInputDevice()?.name ?? "None"}";
            }
        }
        #endregion

        #region Public Methods
        /// <summary>Toggle debug info display</summary>
        public void ToggleDebugInfo()
        {
            m_ShowDebugInfo = !m_ShowDebugInfo;
            
            if (m_FPSText != null) m_FPSText.gameObject.SetActive(m_ShowDebugInfo);
            if (m_CameraInfoText != null) m_CameraInfoText.gameObject.SetActive(m_ShowDebugInfo);
            if (m_InputInfoText != null) m_InputInfoText.gameObject.SetActive(m_ShowDebugInfo);
        }

        /// <summary>Test different camera interpolation types</summary>
        public void TestInterpolationType(int _typeIndex)
        {
            if (m_CameraController == null) return;

            // Cycle through interpolation types for testing
            CameraInterpolationType[] types = {
                CameraInterpolationType.None,
                CameraInterpolationType.SmoothDamp,
                CameraInterpolationType.Lerp,
                CameraInterpolationType.Slerp
            };

            if (_typeIndex >= 0 && _typeIndex < types.Length)
            {
                Debug.Log($"Testing Camera Interpolation: {types[_typeIndex]}");
                // Note: Cần PlayerSettings để thay đổi interpolation type
            }
        }
        #endregion

        #if UNITY_EDITOR
        [ContextMenu("Toggle Debug Info")]
        private void ToggleDebugInfoContext()
        {
            ToggleDebugInfo();
        }

        [ContextMenu("Test SmoothDamp")]
        private void TestSmoothDamp()
        {
            TestInterpolationType(1);
        }

        [ContextMenu("Test Lerp")]
        private void TestLerp()
        {
            TestInterpolationType(2);
        }

        [ContextMenu("Test Slerp")]
        private void TestSlerp()
        {
            TestInterpolationType(3);
        }
        #endif
    }
}
