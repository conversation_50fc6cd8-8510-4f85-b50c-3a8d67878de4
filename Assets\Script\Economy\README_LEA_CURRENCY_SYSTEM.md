# 💰 Hệ Thống Tiền Tệ Lea & Kho Hàng

## 📋 Tổng Quan

Hệ thống Economy hoàn chỉnh cho Unity với tiền tệ Lea, kho hàng người chơi và giao diện UI đẹp mắt.

### ✨ Tính Năng Chính

- 💰 **Hệ thống tiền tệ Lea** với auto-save và validation
- 🎒 **Kho hàng thông minh** với 50 slots và tìm kiếm
- 🎨 **UI animations** và visual effects
- 🔧 **Inspector fields** điều chỉnh được trong Edit mode
- 💾 **Auto-save** dữ liệu định kỳ
- 🎯 **Tích hợp shop** mua/bán vật phẩm
- 🔍 **Debug tools** và Context Menu

---

## 📁 Cấu Trúc Files

```
Assets/Script/Economy/
├── 📄 LeaCurrencyManager.cs          # Quản lý tiền tệ Lea
├── 📄 PlayerInventory.cs             # Quản lý kho hàng
├── 📄 EconomySystemManager.cs        # Manager tổng thể
├── 📄 LeaCurrencyUI.cs               # UI hiển thị tiền
├── 📄 InventoryUI.cs                 # UI kho hàng
├── 📄 HUONG_DAN_CAI_DAT_LEA_CURRENCY_SYSTEM.md  # Hướng dẫn chi tiết
├── 📄 README_LEA_CURRENCY_SYSTEM.md  # File này
└── Editor/
    └── 📄 EconomySystemSetupWizard.cs # Wizard setup tự động
```

---

## 🚀 Cài Đặt Nhanh

### Phương Pháp 1: Sử Dụng Setup Wizard (Khuyến Nghị)

1. **Mở Wizard:**
   ```
   Menu → Economy System → Setup Wizard
   ```

2. **Chọn tùy chọn:**
   - ✅ Tạo Economy System Manager
   - ✅ Tạo Currency UI
   - ✅ Tạo Inventory UI
   - ✅ Tạo Canvas mới

3. **Nhấn "Thiết Lập Tự Động"**

### Phương Pháp 2: Setup Thủ Công

Xem file `HUONG_DAN_CAI_DAT_LEA_CURRENCY_SYSTEM.md` để có hướng dẫn chi tiết từng bước.

---

## 🎮 Cách Sử Dụng

### Trong Code

```csharp
// Lấy reference đến hệ thống
EconomySystemManager economy = FindObjectOfType<EconomySystemManager>();

// Quản lý tiền Lea
economy.CurrencyManager.ThemTien(100);        // Thêm 100 Lea
economy.CurrencyManager.TruTien(50);          // Trừ 50 Lea
bool duTien = economy.CurrencyManager.CoTheMua(200); // Kiểm tra đủ tiền

// Quản lý kho hàng
economy.PlayerInventory.ThemVatPham("wood", 5);     // Thêm 5 gỗ
economy.PlayerInventory.XoaVatPham("wood", 2);      // Xóa 2 gỗ
bool coVatPham = economy.PlayerInventory.CoVatPham("wood", 3); // Kiểm tra có đủ

// Mua/bán vật phẩm
economy.MuaVatPham("sword", 1, 100);         // Mua 1 kiếm giá 100 Lea
economy.BanVatPham("wood", 5, 10);           // Bán 5 gỗ giá gốc 10 Lea/cái
```

### Phím Điều Khiển

| Phím | Chức Năng |
|------|-----------|
| **Tab** | Mở/đóng kho hàng |
| **Esc** | Đóng UI panels |

---

## 🔧 Cấu Hình

### LeaCurrencyManager

```csharp
[Header("💰 Cài Đặt Tiền Tệ Lea")]
public int soTienHienTai = 100;           // Tiền khởi tạo
public int soTienToiDa = 999999;          // Giới hạn tối đa
public bool tuDongLuuTien = true;         // Auto-save
public bool hienThiLog = true;            // Debug logs

[Header("🎨 Hiệu Ứng")]
public bool coHieuUng = true;             // Bật animations
public float thoiGianHieuUng = 0.5f;      // Thời gian animation
```

### PlayerInventory

```csharp
[Header("🎒 Cài Đặt Kho Hàng")]
public int soSlotToiDa = 50;              // Số slot tối đa
public bool tuDongSapXep = true;          // Auto-sort
public bool tuDongLuuKho = true;          // Auto-save
public bool hienThiLog = true;            // Debug logs
```

### EconomySystemManager

```csharp
[Header("🔧 Cài Đặt Hệ Thống")]
public bool tuDongKhoiTao = true;         // Auto-init
public bool hienThiLog = true;            // Debug logs
public bool tuDongTimComponents = true;   // Auto-find components

[Header("🎯 Shop Integration")]
public float phanTramGiamGiaBan = 0.5f;   // Giảm giá khi bán (50%)
```

---

## 🎨 UI Components

### Currency UI
- **Hiển thị số tiền** ở góc màn hình
- **Animations** khi thay đổi tiền
- **Màu sắc** thay đổi theo trạng thái
- **Particle effects** (optional)

### Inventory UI
- **Panel kho hàng** mở bằng Tab
- **Tìm kiếm** vật phẩm theo tên
- **Sắp xếp** tự động
- **Thông tin** số slot và phần trăm đầy

---

## 🔍 Debug & Testing

### Context Menu Commands

#### EconomySystemManager
- Right-click → **"Khởi Tạo Hệ Thống"**
- Right-click → **"Reset Hệ Thống"**
- Right-click → **"Hiển Thị Thông Tin"**
- Right-click → **"Test Mua Vật Phẩm"**
- Right-click → **"Test Bán Vật Phẩm"**

#### LeaCurrencyManager
- Right-click → **"Thêm 1000 Lea"**
- Right-click → **"Trừ 500 Lea"**
- Right-click → **"Reset Tiền"**
- Right-click → **"Hiển Thị Thông Tin"**

#### PlayerInventory
- Right-click → **"Thêm Vật Phẩm Test"**
- Right-click → **"Xóa Vật Phẩm Test"**
- Right-click → **"Hiển Thị Thông Tin Kho"**

### Console Logs
Bật **"Hiển Thị Log"** trong các Manager để xem debug information.

---

## 📊 Events System

### Currency Events
```csharp
LeaCurrencyManager.OnLeaChanged += (newAmount) => {
    Debug.Log($"Tiền thay đổi: {newAmount}");
};

LeaCurrencyManager.OnLeaTransaction += (change, total) => {
    Debug.Log($"Giao dịch: {change:+#;-#;0}, Tổng: {total}");
};

LeaCurrencyManager.OnTransactionFailed += (reason) => {
    Debug.Log($"Giao dịch thất bại: {reason}");
};
```

### Inventory Events
```csharp
PlayerInventory.OnItemAdded += (item) => {
    Debug.Log($"Thêm vật phẩm: {item.ItemId}");
};

PlayerInventory.OnItemRemoved += (item) => {
    Debug.Log($"Xóa vật phẩm: {item.ItemId}");
};

PlayerInventory.OnInventoryFull += (message) => {
    Debug.Log($"Kho đầy: {message}");
};
```

---

## 💾 Data Persistence

### Auto-Save
- **Currency:** Lưu mỗi 30 giây
- **Inventory:** Lưu mỗi 60 giây
- **Trigger:** OnApplicationPause, OnApplicationFocus, OnDestroy

### Manual Save/Load
```csharp
// Lưu thủ công
currencyManager.LuuDuLieu();
inventory.LuuDuLieu();

// Xóa dữ liệu
currencyManager.XoaDuLieu();
inventory.XoaDuLieu();
```

---

## ⚠️ Yêu Cầu Hệ Thống

- **Unity 2021.3+**
- **TextMeshPro** (Import từ Package Manager)
- **Input System** (Optional, cho advanced controls)

---

## 🔗 Tích Hợp

### Với Player System Hiện Tại
```csharp
public class PlayerController : MonoBehaviour 
{
    private EconomySystemManager economy;
    
    void Start() 
    {
        economy = FindObjectOfType<EconomySystemManager>();
    }
    
    void OnTriggerEnter(Collider other) 
    {
        if (other.CompareTag("Coin")) 
        {
            economy.CurrencyManager.ThemTien(10);
            Destroy(other.gameObject);
        }
    }
}
```

### Với Shop System
```csharp
public class ShopItem : MonoBehaviour 
{
    public string itemId = "wood";
    public int price = 10;
    
    public void OnBuyClick() 
    {
        EconomySystemManager economy = FindObjectOfType<EconomySystemManager>();
        economy.MuaVatPham(itemId, 1, price);
    }
}
```

---

## 📞 Hỗ Trợ

### Troubleshooting
1. **UI không hiển thị:** Kiểm tra Canvas và UI references
2. **Không lưu dữ liệu:** Kiểm tra PlayerPrefs permissions
3. **Animation không hoạt động:** Kiểm tra Coroutine settings
4. **Events không fire:** Kiểm tra event subscriptions

### Performance Tips
- Sử dụng **Object Pooling** cho UI elements
- Bật **Auto-save** với interval hợp lý
- Tắt **Debug Logs** trong build release

---

## 🎯 Roadmap

### Planned Features
- [ ] **Multiplayer support** với network sync
- [ ] **Advanced animations** với DOTween
- [ ] **Sound effects** cho transactions
- [ ] **Localization** đa ngôn ngữ
- [ ] **Analytics** tracking

---

**Chúc bạn thành công với hệ thống Economy Lea! 🎮💰**

*Tạo bởi: Economy System Team*  
*Phiên bản: 1.0*  
*Ngày cập nhật: 2024*
