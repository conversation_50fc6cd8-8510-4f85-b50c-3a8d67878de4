# 💰 Hướng Dẫn Cài Đặt Hệ Thống Tiền Tệ Lea & Kho Hàng

## 📋 Tổng Quan

Hệ thống này bao gồm:
- **💰 Hệ thống tiền tệ Lea** với auto-save và visual effects
- **🎒 Kho hàng người chơi** với 50 slots và tìm kiếm
- **🎨 Giao diện UI** hiển thị tiền và kho hàng
- **🔧 Inspector fields** có thể điều chỉnh trong Edit mode

### ✨ Tính Năng Chính

- 💰 **Quản lý tiền Lea** với validation và events
- 🎒 **Kho hàng thông minh** với auto-sort và search
- 🎨 **UI animations** khi thay đổi tiền/vật phẩm
- 💾 **Auto-save** dữ liệu định kỳ
- 🔧 **Context Menu** để test và debug
- 🎯 **Tích hợp shop** mua/bán vật phẩm

---

## 🚀 Phần 1: Thiết Lập Hệ Thống Cơ Bản

### Bước 1: Tạo Economy System Manager

1. **Tạo GameObject chính:**
   ```
   GameObject → Create Empty → "EconomySystem"
   Add Component → Economy System Manager
   Add Component → Lea Currency Manager
   Add Component → Player Inventory
   ```

2. **Cấu hình EconomySystemManager:**
   ```
   System References:
   ├── Currency Manager: [Tự động tìm]
   ├── Player Inventory: [Tự động tìm]
   ├── Currency UI: [Sẽ gán sau]
   └── Inventory UI: [Sẽ gán sau]

   Cài Đặt Hệ Thống:
   ├── Tự Động Khởi Tạo: ✓
   ├── Hiển Thị Log: ✓
   └── Tự Động Tìm Components: ✓

   Cài Đặt Lưu Trữ:
   ├── Tự Động Lưu Data: ✓
   ├── Khoảng Thời Gian Lưu: 60 (giây)
   └── Tạo Backup: ✓

   Shop Integration:
   └── Phần Trăm Giảm Giá Bán: 0.5 (50%)
   ```

### Bước 2: Cấu Hình Currency Manager

1. **Cài đặt LeaCurrencyManager:**
   ```
   💰 Cài Đặt Tiền Tệ Lea:
   ├── Số Tiền Hiện Tại: 100 (Lea khởi tạo)
   ├── Số Tiền Tối Đa: 999999 (Giới hạn)
   ├── Tự Động Lưu Tiền: ✓
   └── Hiển Thị Log: ✓

   💾 Cài Đặt Lưu Trữ:
   ├── Key Lưu Trữ: "LeaCurrency"
   └── Khoảng Thời Gian Lưu: 30 (giây)

   🎨 Hiệu Ứng:
   ├── Có Hiệu Ứng: ✓
   └── Thời Gian Hiệu Ứng: 0.5 (giây)
   ```

### Bước 3: Cấu Hình Player Inventory

1. **Cài đặt PlayerInventory:**
   ```
   🎒 Cài Đặt Kho Hàng:
   ├── Số Slot Tối Đa: 50
   ├── Tự Động Sắp Xếp: ✓
   ├── Tự Động Lưu Kho: ✓
   └── Hiển Thị Log: ✓

   💾 Cài Đặt Lưu Trữ:
   ├── Key Lưu Trữ: "PlayerInventory"
   └── Khoảng Thời Gian Lưu: 60 (giây)
   ```

---

## 🎨 Phần 2: Thiết Lập Giao Diện UI

### Bước 1: Tạo Canvas UI

1. **Tạo Canvas chính:**
   ```
   GameObject → UI → Canvas
   Render Mode: Screen Space - Overlay
   Canvas Scaler: Scale With Screen Size
   Reference Resolution: 1920x1080
   ```

### Bước 2: Tạo Currency UI

1. **Tạo Currency HUD:**
   ```
   Canvas → Create Empty → "CurrencyHUD"
   Position: Top-right corner (Anchor: Top-Right)
   ```

2. **Thêm UI Elements:**
   ```
   CurrencyHUD/
   ├── Background (Image)
   │   ├── Color: (0, 0, 0, 0.5) - Nền trong suốt
   │   └── Size: (200, 60)
   ├── LeaIcon (Image)
   │   ├── Position: Left side
   │   └── Size: (40, 40)
   ├── CurrencyText (TextMeshPro)
   │   ├── Text: "100 Lea"
   │   ├── Font Size: 24
   │   ├── Color: White
   │   └── Alignment: Center
   └── CurrencySlider (Slider) [Optional]
       ├── Fill Color: Gold
       └── Background Color: Dark Gray
   ```

3. **Thêm LeaCurrencyUI Component:**
   ```
   CurrencyHUD → Add Component → Lea Currency UI

   🎨 UI References:
   ├── Text Tiền Tệ: [Gán CurrencyText]
   ├── Icon Tiền Tệ: [Gán LeaIcon]
   ├── Slider Tiền Tệ: [Gán CurrencySlider]
   └── Panel Tiền Tệ: [Gán CurrencyHUD]

   🎭 Animation Settings:
   ├── Có Animation: ✓
   ├── Thời Gian Animation: 0.5
   ├── Curve Tăng Tiền: EaseInOut
   └── Curve Giảm Tiền: EaseInOut

   🌈 Color Settings:
   ├── Màu Bình Thường: White
   ├── Màu Tăng Tiền: Green
   ├── Màu Giảm Tiền: Red
   └── Màu Không Đủ Tiền: Red

   ✨ Effects:
   ├── Có Hiệu Ứng Rung: ✓
   ├── Cường Độ Rung: 5
   ├── Hiệu Ứng Tăng Tiền: [Optional Particle]
   └── Hiệu Ứng Giảm Tiền: [Optional Particle]

   🔧 Settings:
   ├── Định Dạng Tiền: "{0:N0} Lea"
   ├── Hiển Thị Slider: ✓
   └── Hiển Thị Log: ✗
   ```

### Bước 3: Tạo Inventory UI

1. **Tạo Inventory Panel:**
   ```
   Canvas → Create Empty → "InventoryPanel"
   Position: Center screen
   Size: (800, 600)
   Active: False (Ẩn ban đầu)
   ```

2. **Thêm UI Elements:**
   ```
   InventoryPanel/
   ├── Background (Image)
   │   ├── Color: (0.1, 0.1, 0.1, 0.9)
   │   └── Size: Full
   ├── Header/
   │   ├── Title (TextMeshPro): "Kho Hàng"
   │   ├── SlotInfo (TextMeshPro): "25/50"
   │   ├── CurrencyDisplay (TextMeshPro): "1000 Lea"
   │   ├── ProgressBar (Slider): Hiển thị % đầy
   │   └── CloseButton (Button): "X"
   ├── SearchPanel/
   │   ├── SearchInput (TMP_InputField): "Tìm kiếm..."
   │   ├── SearchButton (Button): "Tìm"
   │   └── ClearButton (Button): "Xóa"
   ├── ItemContainer/
   │   ├── ScrollView (Scroll Rect)
   │   └── Content (Vertical Layout Group)
   └── Controls/
       ├── SortButton (Button): "Sắp Xếp"
       └── ClearAllButton (Button): "Xóa Tất Cả"
   ```

3. **Tạo Item Slot Prefab:**
   ```
   Create Empty → "ItemSlotPrefab"
   
   ItemSlotPrefab/
   ├── Background (Image): Nền slot
   ├── ItemIcon (Image): Icon vật phẩm
   ├── ItemName (TextMeshPro): Tên vật phẩm
   ├── ItemQuantity (TextMeshPro): Số lượng
   └── SlotButton (Button): Để click

   Add Component → Inventory Slot UI
   ```

4. **Thêm InventoryUI Component:**
   ```
   InventoryPanel → Add Component → Inventory UI

   🎨 UI References:
   ├── Panel Kho Hàng: [Gán InventoryPanel]
   ├── Container Vật Phẩm: [Gán Content]
   ├── Prefab Slot Vật Phẩm: [Gán ItemSlotPrefab]
   └── Scroll Rect: [Gán ScrollView]

   📊 Thông Tin Kho:
   ├── Text Số Slot: [Gán SlotInfo]
   ├── Slider Đầy Kho: [Gán ProgressBar]
   └── Text Phần Trăm Đầy: [Optional]

   🔍 Tìm Kiếm:
   ├── Input Tìm Kiếm: [Gán SearchInput]
   ├── Button Tìm Kiếm: [Gán SearchButton]
   └── Button Xóa Tìm Kiếm: [Gán ClearButton]

   🔧 Điều Khiển:
   ├── Button Đóng Kho: [Gán CloseButton]
   ├── Button Sắp Xếp: [Gán SortButton]
   └── Button Xóa Toàn Bộ: [Gán ClearAllButton]

   ⚙️ Settings:
   ├── Tự Động Cập Nhật: ✓
   ├── Hiển Thị Log: ✗
   └── Phím Mở Kho: Tab
   ```

---

## 🔗 Phần 3: Tích Hợp Với Hệ Thống Hiện Tại

### Bước 1: Kết Nối UI với Managers

1. **Gán UI References vào EconomySystemManager:**
   ```
   EconomySystem GameObject:
   ├── Currency UI: [Gán LeaCurrencyUI component]
   └── Inventory UI: [Gán InventoryUI component]
   ```

### Bước 2: Tích Hợp Với Player System

1. **Thêm vào Player GameObject:**
   ```
   Player → Add Component → Economy Player Adapter (nếu có)
   
   Hoặc tạo reference trong PlayerController:
   ```

2. **Script tích hợp đơn giản:**
   ```csharp
   // Trong PlayerController hoặc script tương tự
   public EconomySystemManager economySystem;
   
   void Start() {
       economySystem = FindObjectOfType<EconomySystemManager>();
   }
   
   // Sử dụng
   economySystem.MuaVatPham("wood", 5, 10);
   economySystem.BanVatPham("stone", 2, 15);
   ```

---

## 🎮 Phần 4: Hướng Dẫn Sử Dụng

### Controls - Phím Điều Khiển

| Phím | Chức Năng |
|------|-----------|
| **Tab** | Mở/đóng kho hàng |
| **Esc** | Đóng UI panels |

### Cách Sử Dụng Trong Game

#### 💰 Quản Lý Tiền Lea
- **Xem số dư:** Hiển thị ở góc màn hình
- **Animation:** Tự động khi thay đổi tiền
- **Màu sắc:** Xanh khi tăng, đỏ khi giảm

#### 🎒 Quản Lý Kho Hàng
- **Mở kho:** Nhấn Tab
- **Tìm kiếm:** Gõ tên vật phẩm
- **Sắp xếp:** Nhấn nút "Sắp Xếp"
- **Xóa:** Nhấn nút "Xóa Tất Cả"

#### 🏪 Mua/Bán Vật Phẩm
```csharp
// Mua vật phẩm
economySystem.MuaVatPham("wood", 5, 10); // ID, số lượng, giá

// Bán vật phẩm  
economySystem.BanVatPham("wood", 2, 10); // ID, số lượng, giá gốc
```

---

## 🔧 Phần 5: Debug và Testing

### Context Menu Commands

#### EconomySystemManager
- **"Khởi Tạo Hệ Thống"** - Khởi tạo lại toàn bộ
- **"Reset Hệ Thống"** - Reset về mặc định
- **"Hiển Thị Thông Tin"** - Xem trạng thái hệ thống
- **"Test Mua Vật Phẩm"** - Test mua wood
- **"Test Bán Vật Phẩm"** - Test bán wood

#### LeaCurrencyManager
- **"Thêm 1000 Lea"** - Thêm tiền test
- **"Trừ 500 Lea"** - Trừ tiền test
- **"Reset Tiền"** - Reset về 100 Lea
- **"Hiển Thị Thông Tin"** - Xem thông tin tiền

#### PlayerInventory
- **"Thêm Vật Phẩm Test"** - Thêm vật phẩm test
- **"Xóa Vật Phẩm Test"** - Xóa vật phẩm test
- **"Hiển Thị Thông Tin Kho"** - Xem danh sách kho

#### UI Components
- **LeaCurrencyUI:** Test animations
- **InventoryUI:** Mở/đóng kho, cập nhật UI

### Kiểm Tra Hoạt Động

1. **Test Currency System:**
   - Right-click LeaCurrencyManager → "Thêm 1000 Lea"
   - Kiểm tra UI có animation không
   - Kiểm tra màu sắc thay đổi

2. **Test Inventory System:**
   - Right-click PlayerInventory → "Thêm Vật Phẩm Test"
   - Nhấn Tab để mở kho
   - Kiểm tra tìm kiếm và sắp xếp

3. **Test Integration:**
   - Right-click EconomySystemManager → "Test Mua Vật Phẩm"
   - Kiểm tra tiền giảm và vật phẩm tăng
   - Test bán vật phẩm

---

## ⚠️ Lưu Ý Quan Trọng

### Cài Đặt Inspector Fields
- **Tất cả fields đều có thể điều chỉnh trong Edit mode**
- **Không cần Play mode để cấu hình**
- **Sử dụng Context Menu để test nhanh**

### Performance
- **Auto-save:** Mặc định 30-60 giây
- **UI Updates:** Chỉ khi có thay đổi
- **Memory:** Tối ưu với object pooling

### Tương Thích
- **Unity 2021.3+**
- **TextMeshPro required**
- **Input System (optional)**

### Troubleshooting
- **UI không hiển thị:** Kiểm tra Canvas và references
- **Không lưu dữ liệu:** Kiểm tra PlayerPrefs permissions
- **Animation không hoạt động:** Kiểm tra Coroutine settings

---

## 📞 Hỗ Trợ

Nếu gặp vấn đề, kiểm tra:
1. **Console logs** - Bật "Hiển Thị Log" trong các Manager
2. **Component references** - Đảm bảo đã gán đầy đủ
3. **Context Menu** - Sử dụng để debug nhanh

---

## 🏪 Phần 6: Hệ Thống Trading Machine (Máy Mua/Bán)

### Bước 1: Tạo Trading Machine

1. **Tạo GameObject cho Trading Machine:**
   ```
   GameObject → Create Empty → "TradingMachine_Wood"
   Add Component → Trading Machine
   Add Component → Trading Zone Detector
   Add Component → Trading Machine UI
   ```

2. **Cấu hình Trading Machine:**
   ```
   🏪 Cài Đặt Máy:
   ├── Loại Máy: Both (Cả mua và bán)
   ├── Tên Máy: "Máy Giao Dịch Gỗ"
   ├── Item ID: "wood"
   └── Hiển Thị Log: ✓

   💰 Cài Đặt Giá:
   ├── Giá Mua: 10 (Máy mua từ người chơi)
   ├── Giá Bán: 15 (Máy bán cho người chơi)
   ├── Giá Tối Thiểu: 1
   ├── Giá Tối Đa: 1000
   └── Cho Phép Chỉnh Giá: ✓

   📦 Cài Đặt Số Lượng:
   ├── Số Lượng Tối Thiểu: 1
   ├── Số Lượng Tối Đa: 99
   └── Số Lượng Mặc Định: 1

   🎯 Trigger Zone:
   ├── Player Layer: Default
   └── Khoảng Cách Tương Tác: 3
   ```

### Bước 2: Thiết Lập UI cho Trading Machine

1. **Tạo Canvas cho Trading Machine:**
   ```
   TradingMachine_Wood → UI → Canvas
   Render Mode: World Space
   Canvas Scaler: Constant Physical Size
   ```

2. **Tạo Trading Panel:**
   ```
   Canvas → Create Empty → "TradingPanel"

   TradingPanel/
   ├── Background (Image)
   │   ├── Color: (0.1, 0.1, 0.1, 0.9)
   │   └── Size: (400, 300)
   ├── Header/
   │   ├── MachineTitle (TextMeshPro): "Máy Giao Dịch Gỗ"
   │   ├── ItemName (TextMeshPro): "Gỗ"
   │   └── CurrentMoney (TextMeshPro): "Tiền: 1000 Lea"
   ├── PriceInfo/
   │   ├── BuyPriceText (TextMeshPro): "Giá mua: 10 Lea"
   │   ├── SellPriceText (TextMeshPro): "Giá bán: 15 Lea"
   │   └── TotalPriceText (TextMeshPro): "Tổng: 15 Lea"
   ├── QuantityControl/
   │   ├── QuantitySlider (Slider): Min=1, Max=99
   │   └── QuantityText (TextMeshPro): "1"
   ├── Buttons/
   │   ├── BuyButton (Button): "Mua"
   │   ├── SellButton (Button): "Bán"
   │   ├── AdjustPriceButton (Button): "Chỉnh Giá"
   │   └── CloseButton (Button): "Đóng"
   └── PromptPanel/
       └── PromptText (TextMeshPro): "Nhấn [F] để tương tác"
   ```

3. **Tạo Price Adjustment Panel:**
   ```
   Canvas → Create Empty → "PriceAdjustmentPanel"

   PriceAdjustmentPanel/
   ├── Background (Image)
   ├── Title (TextMeshPro): "Chỉnh Giá"
   ├── BuyPriceControl/
   │   ├── Label (TextMeshPro): "Giá Mua:"
   │   ├── BuyPriceSlider (Slider): Min=1, Max=1000
   │   └── BuyPriceInput (TMP_InputField)
   ├── SellPriceControl/
   │   ├── Label (TextMeshPro): "Giá Bán:"
   │   ├── SellPriceSlider (Slider): Min=1, Max=1000
   │   └── SellPriceInput (TMP_InputField)
   └── Buttons/
       ├── SaveButton (Button): "Lưu"
       └── CancelButton (Button): "Hủy"
   ```

### Bước 3: Cấu Hình Components

1. **Trading Machine Component:**
   ```
   🎨 UI References:
   ├── UI Canvas: [Gán Canvas]
   ├── Panel Giao Dịch: [Gán TradingPanel]
   ├── Panel Chỉnh Giá: [Gán PriceAdjustmentPanel]
   ├── Text Tên Máy: [Gán MachineTitle]
   ├── Text Item Name: [Gán ItemName]
   ├── Button Mua: [Gán BuyButton]
   ├── Button Bán: [Gán SellButton]
   ├── Button Đóng: [Gán CloseButton]
   ├── Button Chỉnh Giá: [Gán AdjustPriceButton]
   ├── Slider Số Lượng: [Gán QuantitySlider]
   ├── Text Số Lượng: [Gán QuantityText]
   ├── Text Giá Mua: [Gán BuyPriceText]
   ├── Text Giá Bán: [Gán SellPriceText]
   ├── Text Tổng Tiền: [Gán TotalPriceText]
   ├── Button Lưu Giá: [Gán SaveButton]
   ├── Button Huy Chỉnh Giá: [Gán CancelButton]
   ├── Slider Giá Mua: [Gán BuyPriceSlider]
   ├── Slider Giá Bán: [Gán SellPriceSlider]
   ├── Input Giá Mua: [Gán BuyPriceInput]
   └── Input Giá Bán: [Gán SellPriceInput]
   ```

2. **Trading Zone Detector:**
   ```
   🎯 Zone Settings:
   ├── Player Layer: Default
   ├── Khoảng Cách Tương Tác: 3
   ├── Hiển Thị Gizmos: ✓
   └── Màu Gizmos: Yellow

   🔧 Settings:
   ├── Hiển Thị Log: ✓
   └── Tự Động Tìm Trading Machine: ✓
   ```

3. **Trading Machine UI:**
   ```
   🎨 Main UI References:
   ├── Main Canvas: [Gán Canvas]
   ├── Panel Giao Dịch: [Gán TradingPanel]
   ├── Panel Chỉnh Giá: [Gán PriceAdjustmentPanel]
   └── Panel Prompt: [Gán PromptPanel]

   🎭 Animation Settings:
   ├── Thời Gian Fade In: 0.3
   ├── Thời Gian Fade Out: 0.2
   ├── Có Hiệu Ứng Scale: ✓
   └── Scale Hiệu Ứng: 1.1

   🌈 Color Settings:
   ├── Màu Button Bình Thường: White
   ├── Màu Button Mua: Green
   ├── Màu Button Bán: Yellow
   └── Màu Button Không Hoạt Động: Gray
   ```

### Bước 4: Thiết Lập Trigger Zone

1. **Cấu hình Collider:**
   ```
   TradingMachine_Wood → Add Component → Box Collider
   ├── Is Trigger: ✓
   ├── Size: (6, 4, 6) - Vùng tương tác
   └── Center: (0, 2, 0)
   ```

2. **Thiết lập Layer:**
   ```
   Player GameObject → Layer: Player (hoặc layer tương ứng)
   Trading Zone Detector → Player Layer: Player
   ```

### Bước 5: Tạo Visual Indicators

1. **Tạo Machine Model:**
   ```
   TradingMachine_Wood/
   ├── Model/
   │   ├── Base (Cube): Thân máy
   │   ├── Screen (Quad): Màn hình hiển thị
   │   └── Buttons (Cubes): Nút bấm
   ├── Indicators/
   │   ├── BuyIndicator (Sphere): Màu xanh lá
   │   ├── SellIndicator (Sphere): Màu vàng
   │   └── ZoneIndicator (Cylinder): Hiển thị vùng
   └── Effects/
       ├── BuyEffect (Particle System)
       ├── SellEffect (Particle System)
       └── ZoneLight (Light)
   ```

2. **Gán Effects vào Trading Machine:**
   ```
   ✨ Effects:
   ├── Effect Mua: [Gán BuyEffect]
   ├── Effect Bán: [Gán SellEffect]
   ├── Indicator Mua: [Gán BuyIndicator]
   ├── Indicator Bán: [Gán SellIndicator]
   └── Audio Source: [Gán AudioSource]
   ```

---

## 🎮 Phần 7: Sử Dụng Trading Machine

### Controls - Phím Điều Khiển

| Phím | Chức Năng |
|------|-----------|
| **F** | Mở/đóng Trading Machine UI |
| **Esc** | Đóng UI |
| **Mouse** | Điều chỉnh slider và input |

### Cách Sử Dụng

#### 💰 Mua Vật Phẩm (Từ Máy)
1. **Bước vào trigger zone** → Thấy prompt "Nhấn [F]"
2. **Nhấn F** → Mở UI Trading Machine
3. **Điều chỉnh số lượng** → Dùng slider
4. **Kiểm tra giá** → Xem "Giá bán" và "Tổng tiền"
5. **Nhấn "Mua"** → Xác nhận giao dịch
6. **Kiểm tra kết quả** → Tiền giảm, vật phẩm vào kho

#### 🏪 Bán Vật Phẩm (Cho Máy)
1. **Bước vào trigger zone** → Thấy prompt
2. **Mở UI** → Nhấn F
3. **Điều chỉnh số lượng** → Số lượng muốn bán
4. **Kiểm tra giá** → Xem "Giá mua" (máy mua từ bạn)
5. **Nhấn "Bán"** → Xác nhận giao dịch
6. **Kiểm tra kết quả** → Vật phẩm giảm, tiền tăng

#### ⚙️ Chỉnh Giá (Admin)
1. **Mở Trading Machine UI**
2. **Nhấn "Chỉnh Giá"** → Mở panel chỉnh giá
3. **Điều chỉnh giá mua/bán** → Dùng slider hoặc input
4. **Nhấn "Lưu"** → Áp dụng giá mới
5. **Hoặc "Hủy"** → Không thay đổi

### Các Loại Máy

#### 🛒 Máy Chỉ Bán (Sell Only)
- Người chơi chỉ có thể **mua** từ máy
- Button "Bán" bị vô hiệu hóa
- Phù hợp cho: Shop NPC, máy bán đồ

#### 💰 Máy Chỉ Mua (Buy Only)
- Người chơi chỉ có thể **bán** cho máy
- Button "Mua" bị vô hiệu hóa
- Phù hợp cho: Thu mua nguyên liệu, recycling

#### 🔄 Máy Đa Năng (Both)
- Có thể cả mua và bán
- Linh hoạt nhất
- Phù hợp cho: Trading post, general store

---

## 🔧 Phần 8: Debug Trading Machine

### Context Menu Commands

#### TradingMachine
- **"Test Mở UI"** - Test mở giao diện
- **"Test Đóng UI"** - Test đóng giao diện
- **"Hiển Thị Thông Tin"** - Xem trạng thái máy

#### TradingZoneDetector
- **"Test Player Vào Zone"** - Giả lập player vào
- **"Test Player Rời Zone"** - Giả lập player rời
- **"Hiển Thị Thông Tin Zone"** - Xem thông tin zone
- **"Xóa Tất Cả Players"** - Reset zone

#### TradingMachineUI
- **"Test Hiển Thị UI"** - Test animation hiển thị
- **"Test Ẩn UI"** - Test animation ẩn
- **"Test Animation Button"** - Test hiệu ứng button

### Kiểm Tra Hoạt Động

1. **Test Trigger Zone:**
   - Di chuyển Player vào/ra zone
   - Kiểm tra Gizmos trong Scene view
   - Xem Console logs

2. **Test UI:**
   - Nhấn F để mở/đóng
   - Kiểm tra animations
   - Test các buttons

3. **Test Trading:**
   - Thử mua vật phẩm
   - Thử bán vật phẩm
   - Kiểm tra tiền và inventory

4. **Test Price Adjustment:**
   - Mở panel chỉnh giá
   - Thay đổi giá mua/bán
   - Lưu và kiểm tra

---

## ⚠️ Lưu Ý Trading Machine

### Yêu Cầu
- **EconomySystemManager** phải được khởi tạo trước
- **Player** phải có layer phù hợp
- **UI Canvas** phải được cấu hình đúng

### Performance
- Sử dụng **Object Pooling** cho nhiều máy
- **Disable UI** khi không sử dụng
- **Optimize Gizmos** trong build

### Tương Thích
- Tích hợp với **Player System** hiện tại
- Hoạt động với **Economy System**
- Hỗ trợ **Multiple Players** (multiplayer ready)

**Chúc bạn thành công với hệ thống Economy Lea & Trading Machine! 🎮💰🏪**
