using UnityEngine;
using System.Collections;

namespace EconomySystem
{
    /// <summary>
    /// Quản lý tổng thể hệ thống Economy
    /// Tích hợp LeaCurrencyManager và PlayerInventory
    /// </summary>
    public class EconomySystemManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎮 System References")]
        [SerializeField] private LeaCurrencyManager m_CurrencyManager;
        [SerializeField] private PlayerInventory m_PlayerInventory;
        [SerializeField] private LeaCurrencyUI m_CurrencyUI;
        [SerializeField] private InventoryUI m_InventoryUI;

        [Header("🔧 Cài Đặt Hệ Thống")]
        [SerializeField] private bool m_TuDongKhoiTao = true;
        [SerializeField] private bool m_HienThiLog = true;
        [SerializeField] private bool m_TuDongTimComponents = true;

        [Header("💾 Cài Đặt Lưu Trữ")]
        [SerializeField] private bool m_TuDongLuuData = true;
        [SerializeField] private float m_KhoangThoiGianLuu = 60f;
        [SerializeField] private bool m_TaoBackup = true;

        [Header("🎯 Shop Integration")]
        [SerializeField] private float m_PhanTramGiamGiaBan = 0.5f;
        #endregion

        #region Properties
        public LeaCurrencyManager CurrencyManager => m_CurrencyManager;
        public PlayerInventory PlayerInventory => m_PlayerInventory;
        public bool HeThongSanSang { get; private set; } = false;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (m_TuDongTimComponents)
                TimCacComponent();

            if (m_TuDongKhoiTao)
                KhoiTaoHeThong();
        }

        private void Start()
        {
            if (m_TuDongLuuData)
            {
                InvokeRepeating(nameof(LuuToanBoData), m_KhoangThoiGianLuu, m_KhoangThoiGianLuu);
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && m_TuDongLuuData)
                LuuToanBoData();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && m_TuDongLuuData)
                LuuToanBoData();
        }

        private void OnDestroy()
        {
            if (m_TuDongLuuData)
                LuuToanBoData();
        }
        #endregion

        #region System Management
        public void KhoiTaoHeThong()
        {
            Log("Bắt đầu khởi tạo hệ thống Economy...");

            // Kiểm tra các component cần thiết
            if (!KiemTraComponents())
            {
                LogLoi("Thiếu components cần thiết!");
                return;
            }

            // Khởi tạo từng hệ thống con
            KhoiTaoCurrencySystem();
            KhoiTaoInventorySystem();
            KhoiTaoUISystem();

            HeThongSanSang = true;
            Log("Hệ thống Economy đã sẵn sàng!");
        }

        private void KhoiTaoCurrencySystem()
        {
            if (m_CurrencyManager != null)
            {
                Log("Currency System đã khởi tạo");
            }
            else
            {
                LogLoi("Không tìm thấy LeaCurrencyManager!");
            }
        }

        private void KhoiTaoInventorySystem()
        {
            if (m_PlayerInventory != null)
            {
                Log("Inventory System đã khởi tạo");
            }
            else
            {
                LogLoi("Không tìm thấy PlayerInventory!");
            }
        }

        private void KhoiTaoUISystem()
        {
            if (m_CurrencyUI != null)
                Log("Currency UI đã khởi tạo");

            if (m_InventoryUI != null)
                Log("Inventory UI đã khởi tạo");
        }

        public void ResetHeThong()
        {
            Log("Reset hệ thống Economy...");

            if (m_CurrencyManager != null)
                m_CurrencyManager.ResetTien();

            if (m_PlayerInventory != null)
                m_PlayerInventory.XoaToanBoKho();

            Log("Đã reset hệ thống Economy");
        }
        #endregion

        #region Shop Operations
        /// <summary>
        /// Mua vật phẩm từ shop
        /// </summary>
        public bool MuaVatPham(string itemId, int soLuong, int giaMua)
        {
            if (!HeThongSanSang)
            {
                LogLoi("Hệ thống chưa sẵn sàng!");
                return false;
            }

            int tongTien = giaMua * soLuong;

            // Kiểm tra đủ tiền
            if (!m_CurrencyManager.CoTheMua(tongTien))
            {
                LogLoi($"Không đủ tiền để mua {itemId}! Cần: {tongTien}, Có: {m_CurrencyManager.SoTienHienTai}");
                return false;
            }

            // Kiểm tra chỗ trong kho
            if (m_PlayerInventory.KhoDay && !m_PlayerInventory.CoVatPham(itemId))
            {
                LogLoi("Kho hàng đã đầy!");
                return false;
            }

            // Thực hiện giao dịch
            if (m_CurrencyManager.TruTien(tongTien))
            {
                if (m_PlayerInventory.ThemVatPham(itemId, soLuong))
                {
                    Log($"Đã mua {soLuong} {itemId} với giá {tongTien} Lea");
                    return true;
                }
                else
                {
                    // Hoàn tiền nếu không thể thêm vào kho
                    m_CurrencyManager.ThemTien(tongTien);
                    LogLoi("Không thể thêm vật phẩm vào kho!");
                    return false;
                }
            }

            return false;
        }

        /// <summary>
        /// Bán vật phẩm cho shop
        /// </summary>
        public bool BanVatPham(string itemId, int soLuong, int giaBan)
        {
            if (!HeThongSanSang)
            {
                LogLoi("Hệ thống chưa sẵn sàng!");
                return false;
            }

            // Kiểm tra có vật phẩm trong kho
            if (!m_PlayerInventory.CoVatPham(itemId, soLuong))
            {
                LogLoi($"Không đủ {itemId} để bán! Cần: {soLuong}, Có: {m_PlayerInventory.LaySoLuongVatPham(itemId)}");
                return false;
            }

            // Tính tiền nhận được (có giảm giá)
            int tienNhan = Mathf.RoundToInt(giaBan * soLuong * m_PhanTramGiamGiaBan);

            // Thực hiện giao dịch
            if (m_PlayerInventory.XoaVatPham(itemId, soLuong))
            {
                m_CurrencyManager.ThemTien(tienNhan);
                Log($"Đã bán {soLuong} {itemId} nhận được {tienNhan} Lea");
                return true;
            }

            return false;
        }
        #endregion

        #region Data Management
        private void LuuToanBoData()
        {
            Log("Lưu toàn bộ dữ liệu Economy...");

            // Các manager sẽ tự động lưu dữ liệu của mình
            // Có thể thêm logic backup ở đây nếu cần

            if (m_TaoBackup)
                TaoBackupData();
        }

        private void TaoBackupData()
        {
            // Logic tạo backup có thể được implement ở đây
            string timestamp = System.DateTime.Now.ToString("yyyyMMdd_HHmmss");
            Log($"Tạo backup dữ liệu: {timestamp}");
        }

        public void XoaToanBoData()
        {
            Log("Xóa toàn bộ dữ liệu Economy...");

            if (m_CurrencyManager != null)
                m_CurrencyManager.XoaDuLieu();

            if (m_PlayerInventory != null)
                m_PlayerInventory.XoaDuLieu();

            Log("Đã xóa toàn bộ dữ liệu");
        }
        #endregion

        #region Utility Methods
        private void TimCacComponent()
        {
            if (m_CurrencyManager == null)
                m_CurrencyManager = GetComponent<LeaCurrencyManager>();

            if (m_PlayerInventory == null)
                m_PlayerInventory = GetComponent<PlayerInventory>();

            if (m_CurrencyUI == null)
                m_CurrencyUI = FindObjectOfType<LeaCurrencyUI>();

            if (m_InventoryUI == null)
                m_InventoryUI = FindObjectOfType<InventoryUI>();
        }

        private bool KiemTraComponents()
        {
            bool hopLe = true;

            if (m_CurrencyManager == null)
            {
                LogLoi("Thiếu LeaCurrencyManager!");
                hopLe = false;
            }

            if (m_PlayerInventory == null)
            {
                LogLoi("Thiếu PlayerInventory!");
                hopLe = false;
            }

            return hopLe;
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[EconomySystemManager] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[EconomySystemManager] {message}");
        }

        public string LayThongTinHeThong()
        {
            string thongTin = "=== THÔNG TIN HỆ THỐNG ECONOMY ===\n";
            thongTin += $"Trạng thái: {(HeThongSanSang ? "Sẵn sàng" : "Chưa sẵn sàng")}\n";
            
            if (m_CurrencyManager != null)
                thongTin += $"Tiền Lea: {m_CurrencyManager.DinhDangTienHienTai()}\n";
            
            if (m_PlayerInventory != null)
                thongTin += $"Kho hàng: {m_PlayerInventory.SoSlotDaDung}/{m_PlayerInventory.SoSlotDaDung + m_PlayerInventory.SoSlotConLai} slots\n";
            
            return thongTin;
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Khởi Tạo Hệ Thống")]
        private void KhoiTaoHeThongMenu()
        {
            KhoiTaoHeThong();
        }

        [ContextMenu("Reset Hệ Thống")]
        private void ResetHeThongMenu()
        {
            ResetHeThong();
        }

        [ContextMenu("Hiển Thị Thông Tin")]
        private void HienThiThongTinMenu()
        {
            Debug.Log(LayThongTinHeThong());
        }

        [ContextMenu("Test Mua Vật Phẩm")]
        private void TestMuaVatPham()
        {
            MuaVatPham("wood", 5, 10);
        }

        [ContextMenu("Test Bán Vật Phẩm")]
        private void TestBanVatPham()
        {
            BanVatPham("wood", 2, 10);
        }
        #endif
        #endregion
    }
}
