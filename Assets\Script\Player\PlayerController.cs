using UnityEngine;
using UnityEngine.InputSystem;

namespace PlayerSystem
{
    /// <summary>
    /// <PERSON><PERSON> thống điều khiển nhân vật ch<PERSON>h với CharacterController
    /// Hỗ trợ First/Third Person, movement, jump, crouch, sprint
    /// Tí<PERSON> hợp với Economy System hiện có
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    [RequireComponent(typeof(PlayerInput))]
    public class PlayerController : MonoBehaviour
    {
        #region Constants
        private const float c_GroundCheckDistance = 0.4f;
        private const float c_MinMoveThreshold = 0.01f;
        private const float c_FallMultiplier = 2.5f;
        private const float c_LowJumpMultiplier = 2f;
        #endregion

        #region Private Fields
        [Header("Movement Settings")]
        [SerializeField, Range(1f, 10f), Tooltip("Tốc độ đi bộ cơ bản")]
        private float m_WalkSpeed = 5f;
        
        [SerializeField, Range(5f, 15f), Tooltip("Tốc độ chạy nhanh")]
        private float m_SprintSpeed = 8f;
        
        [SerializeField, Range(0.5f, 5f), <PERSON><PERSON><PERSON>("Tốc độ khi cúi người")]
        private float m_CrouchSpeed = 2f;
        
        [SerializeField, Range(5f, 20f), Tooltip("Tốc độ tăng tốc")]
        private float m_Acceleration = 10f;
        
        [SerializeField, Range(5f, 20f), Tooltip("Tốc độ giảm tốc")]
        private float m_Deceleration = 10f;

        [Header("Jump Settings")]
        [SerializeField, Range(1f, 5f), Tooltip("Độ cao nhảy")]
        private float m_JumpHeight = 2f;
        
        [SerializeField, Range(-20f, -5f), Tooltip("Lực trọng lực")]
        private float m_Gravity = -15f;
        
        [SerializeField, Range(0f, 1f), Tooltip("Khả năng điều khiển trong không khí")]
        private float m_AirControl = 0.3f;

        [Header("Ground Check")]
        [SerializeField, Tooltip("Transform để kiểm tra mặt đất")]
        private Transform m_GroundCheck;
        
        [SerializeField, Tooltip("Layer mask cho mặt đất")]
        private LayerMask m_GroundMask = 1;

        [Header("Component References")]
        [SerializeField, Tooltip("Transform gốc của camera system")]
        private Transform m_CameraRoot;

        [SerializeField, Tooltip("Camera controller component")]
        private CameraController m_CameraController;

        [SerializeField, Tooltip("Player settings ScriptableObject")]
        private PlayerSettings m_PlayerSettings;

        [Header("Crouch Settings")]
        [SerializeField, Range(0.5f, 2f), Tooltip("Chiều cao khi cúi người")]
        private float m_CrouchHeight = 1f;

        [SerializeField, Range(1.5f, 3f), Tooltip("Chiều cao bình thường")]
        private float m_StandHeight = 2f;

        [Header("Movement Settings")]
        [SerializeField, Tooltip("Di chuyển theo hướng camera (true) hay theo hướng player (false)")]
        private bool m_CameraRelativeMovement = true;

        [Header("Input Debug")]
        [SerializeField, Tooltip("Đảo ngược input X (trái/phải)")]
        private bool m_InvertInputX = false;

        [SerializeField, Tooltip("Đảo ngược input Y (tiến/lùi)")]
        private bool m_InvertInputY = false;

        [SerializeField, Tooltip("Hiển thị debug movement info")]
        private bool m_ShowMovementDebug = false;

        // Component references
        private CharacterController m_CharacterController;
        private PlayerInput m_PlayerInput;
        private PlayerInputHandler m_InputHandler;
        private PlayerMovement m_MovementSystem;

        // Movement state
        private Vector2 m_MoveInput;
        private Vector3 m_Velocity;
        private Vector3 m_MoveDirection;
        private bool m_IsGrounded;
        private bool m_IsSprinting;
        private bool m_IsCrouching;
        private bool m_JumpPressed;
        private bool m_CanJump = true;
        private float m_CurrentSpeed;
        private float m_TargetSpeed;
        #endregion

        #region Public Properties
        /// <summary>Kiểm tra xem nhân vật có đang đứng trên mặt đất không</summary>
        public bool IsGrounded => m_IsGrounded;
        
        /// <summary>Kiểm tra xem nhân vật có đang chạy nhanh không</summary>
        public bool IsSprinting => m_IsSprinting;
        
        /// <summary>Kiểm tra xem nhân vật có đang cúi người không</summary>
        public bool IsCrouching => m_IsCrouching;
        
        /// <summary>Tốc độ hiện tại của nhân vật</summary>
        public float CurrentSpeed => m_CurrentSpeed;
        
        /// <summary>Transform gốc của camera</summary>
        public Transform CameraRoot => m_CameraRoot;
        
        /// <summary>Camera controller component</summary>
        public CameraController CameraController => m_CameraController;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeComponents();
            SetupGroundCheck();
        }

        private void Start()
        {
            SetupInitialState();
        }

        private void Update()
        {
            UpdateGroundCheck();
            UpdateMovement();
            UpdateGravity();
            ApplyMovement();
        }

        private void OnDestroy()
        {
            CleanupComponents();
        }

        private void OnDrawGizmosSelected()
        {
            DrawDebugGizmos();
        }
        #endregion

        #region Input Event Handlers
        /// <summary>Xử lý input di chuyển từ InputHandler</summary>
        private void HandleMoveInput(Vector2 _moveInput)
        {
            m_MoveInput = _moveInput;
        }

        /// <summary>Xử lý input nhảy pressed từ InputHandler</summary>
        private void HandleJumpPressed()
        {
            m_JumpPressed = true;
        }

        /// <summary>Xử lý input nhảy released từ InputHandler</summary>
        private void HandleJumpReleased()
        {
            m_JumpPressed = false;
        }

        /// <summary>Xử lý thay đổi sprint state từ InputHandler</summary>
        private void HandleSprintChanged(bool _isSprinting)
        {
            m_IsSprinting = _isSprinting;
        }

        /// <summary>Xử lý input cúi người từ InputHandler</summary>
        private void HandleCrouchPressed()
        {
            ToggleCrouch();
        }

        /// <summary>Xử lý input look từ InputHandler</summary>
        private void HandleLookInput(Vector2 _lookInput)
        {
            if (m_CameraController != null)
            {
                m_CameraController.OnLook(_lookInput);
            }
        }

        /// <summary>Xử lý input chuyển đổi camera từ InputHandler</summary>
        private void HandleCameraToggle()
        {
            if (m_CameraController != null)
            {
                m_CameraController.ToggleCameraMode();
            }
        }

        /// <summary>Legacy Input Callbacks cho Input System (giữ để tương thích)</summary>
        public void OnMove(InputAction.CallbackContext _context)
        {
            m_MoveInput = _context.ReadValue<Vector2>();
        }

        public void OnMove(Vector2 _moveInput)
        {
            m_MoveInput = _moveInput;
        }

        /// <summary>Parameterless OnMove for Input System compatibility</summary>
        public void OnMove()
        {
            // This method exists for Input System compatibility
            // The actual input handling is done through the InputHandler events
        }

        /// <summary>OnMove with InputValue for Input System compatibility</summary>
        public void OnMove(UnityEngine.InputSystem.InputValue _value)
        {
            Vector2 moveInput = _value.Get<Vector2>();
            m_MoveInput = moveInput;
        }

        public void OnJump(InputAction.CallbackContext _context)
        {
            if (_context.performed)
            {
                m_JumpPressed = true;
            }
            else if (_context.canceled)
            {
                m_JumpPressed = false;
            }
        }

        public void OnJump(bool _pressed)
        {
            m_JumpPressed = _pressed;
        }

        /// <summary>Parameterless OnJump for Input System compatibility</summary>
        public void OnJump()
        {
            m_JumpPressed = true;
        }

        /// <summary>OnJump with InputValue for Input System compatibility</summary>
        public void OnJump(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed)
            {
                m_JumpPressed = true;
            }
            else
            {
                m_JumpPressed = false;
            }
        }

        public void OnSprint(InputAction.CallbackContext _context)
        {
            m_IsSprinting = _context.ReadValue<float>() > 0.5f;
        }

        public void OnSprint(bool _pressed)
        {
            m_IsSprinting = _pressed;
        }

        /// <summary>OnSprint with InputValue for Input System compatibility</summary>
        public void OnSprint(UnityEngine.InputSystem.InputValue _value)
        {
            m_IsSprinting = _value.isPressed;
        }

        public void OnCrouch(InputAction.CallbackContext _context)
        {
            if (_context.performed)
            {
                ToggleCrouch();
            }
        }

        public void OnCrouch()
        {
            ToggleCrouch();
        }

        /// <summary>OnCrouch with InputValue for Input System compatibility</summary>
        public void OnCrouch(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed)
            {
                ToggleCrouch();
            }
        }

        public void OnToggleCamera(InputAction.CallbackContext _context)
        {
            if (_context.performed && m_CameraController != null)
            {
                m_CameraController.ToggleCameraMode();
            }
        }

        public void OnToggleCamera()
        {
            if (m_CameraController != null)
            {
                m_CameraController.ToggleCameraMode();
            }
        }

        /// <summary>OnToggleCamera with InputValue for Input System compatibility</summary>
        public void OnToggleCamera(UnityEngine.InputSystem.InputValue _value)
        {
            if (_value.isPressed && m_CameraController != null)
            {
                m_CameraController.ToggleCameraMode();
            }
        }
        #endregion

        #region Private Methods
        private void InitializeComponents()
        {
            m_CharacterController = GetComponent<CharacterController>();
            m_PlayerInput = GetComponent<PlayerInput>();
            m_InputHandler = GetComponent<PlayerInputHandler>();

            if (m_CharacterController == null)
            {
                Debug.LogError("PlayerController: CharacterController component không tìm thấy!", this);
            }

            if (m_PlayerInput == null)
            {
                Debug.LogError("PlayerController: PlayerInput component không tìm thấy!", this);
            }

            if (m_InputHandler == null)
            {
                Debug.LogError("PlayerController: PlayerInputHandler component không tìm thấy!", this);
            }

            // Initialize movement system
            m_MovementSystem = new PlayerMovement();

            // Subscribe to input events
            if (m_InputHandler != null)
            {
                m_InputHandler.OnMoveInput += HandleMoveInput;
                m_InputHandler.OnLookInput += HandleLookInput;
                m_InputHandler.OnJumpPressed += HandleJumpPressed;
                m_InputHandler.OnJumpReleased += HandleJumpReleased;
                m_InputHandler.OnSprintChanged += HandleSprintChanged;
                m_InputHandler.OnCrouchPressed += HandleCrouchPressed;
                m_InputHandler.OnCameraTogglePressed += HandleCameraToggle;
            }
        }

        private void CleanupComponents()
        {
            // Unsubscribe from input events
            if (m_InputHandler != null)
            {
                m_InputHandler.OnMoveInput -= HandleMoveInput;
                m_InputHandler.OnJumpPressed -= HandleJumpPressed;
                m_InputHandler.OnJumpReleased -= HandleJumpReleased;
                m_InputHandler.OnSprintChanged -= HandleSprintChanged;
                m_InputHandler.OnCrouchPressed -= HandleCrouchPressed;
                m_InputHandler.OnCameraTogglePressed -= HandleCameraToggle;
            }
        }

        private void SetupGroundCheck()
        {
            if (m_GroundCheck == null)
            {
                // Tự động tạo ground check nếu chưa có
                GameObject groundCheckObj = new GameObject("GroundCheck");
                groundCheckObj.transform.SetParent(transform);
                groundCheckObj.transform.localPosition = new Vector3(0, -m_CharacterController.height * 0.5f, 0);
                m_GroundCheck = groundCheckObj.transform;
            }
        }

        private void SetupInitialState()
        {
            m_StandHeight = m_CharacterController.height;
            m_CurrentSpeed = 0f;
            m_TargetSpeed = 0f;
            
            // Lock cursor cho FPS controls
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        private void UpdateGroundCheck()
        {
            m_IsGrounded = Physics.CheckSphere(m_GroundCheck.position, c_GroundCheckDistance, m_GroundMask);
            
            // Reset jump ability khi chạm đất
            if (m_IsGrounded && m_Velocity.y < 0)
            {
                m_CanJump = true;
                m_Velocity.y = -2f; // Giữ nhân vật sát mặt đất
            }
        }

        private void UpdateMovement()
        {
            // Tính toán target speed dựa trên input và state
            CalculateTargetSpeed();
            
            // Smooth speed transition
            m_CurrentSpeed = Mathf.MoveTowards(m_CurrentSpeed, m_TargetSpeed, 
                (m_TargetSpeed > m_CurrentSpeed ? m_Acceleration : m_Deceleration) * Time.deltaTime);
            
            // Tính toán hướng di chuyển
            CalculateMoveDirection();
            
            // Xử lý nhảy
            HandleJump();
        }

        private void CalculateTargetSpeed()
        {
            float inputMagnitude = m_MoveInput.magnitude;
            
            if (inputMagnitude < c_MinMoveThreshold)
            {
                m_TargetSpeed = 0f;
                return;
            }
            
            // Chọn tốc độ dựa trên state
            if (m_IsCrouching)
            {
                m_TargetSpeed = m_CrouchSpeed;
            }
            else if (m_IsSprinting)
            {
                m_TargetSpeed = m_SprintSpeed;
            }
            else
            {
                m_TargetSpeed = m_WalkSpeed;
            }
            
            // Áp dụng input magnitude
            m_TargetSpeed *= inputMagnitude;
        }

        private void CalculateMoveDirection()
        {
            if (m_MoveInput.magnitude < c_MinMoveThreshold)
            {
                m_MoveDirection = Vector3.zero;
                return;
            }

            // Áp dụng invert settings
            float inputX = m_InvertInputX ? -m_MoveInput.x : m_MoveInput.x;
            float inputY = m_InvertInputY ? -m_MoveInput.y : m_MoveInput.y;

            // Tính toán movement direction
            if (m_CameraRelativeMovement && m_CameraController != null && m_CameraController.PlayerCamera != null)
            {
                // Camera-relative movement: di chuyển theo hướng camera
                Transform cameraTransform = m_CameraController.PlayerCamera.transform;

                // Lấy forward và right direction của camera, nhưng flatten trên mặt phẳng Y
                Vector3 cameraForward = cameraTransform.forward;
                cameraForward.y = 0f;
                cameraForward.Normalize();

                Vector3 cameraRight = cameraTransform.right;
                cameraRight.y = 0f;
                cameraRight.Normalize();

                // Tính toán movement direction dựa trên camera orientation
                m_MoveDirection = (cameraRight * inputX + cameraForward * inputY).normalized;
            }
            else
            {
                // Player-relative movement: di chuyển theo hướng player (cách cũ)
                Vector3 inputDirection = new Vector3(inputX, 0f, inputY);
                m_MoveDirection = transform.TransformDirection(inputDirection);
                m_MoveDirection.y = 0f;
                m_MoveDirection.Normalize();
            }

            // Debug info
            if (m_ShowMovementDebug)
            {
                string movementType = m_CameraRelativeMovement ? "Camera-Relative" : "Player-Relative";
                Debug.Log($"[{movementType}] Input: ({m_MoveInput.x:F2}, {m_MoveInput.y:F2}) -> " +
                         $"Processed: ({inputX:F2}, {inputY:F2}) -> " +
                         $"Final Direction: {m_MoveDirection}");
            }

            // Áp dụng air control khi không ở trên mặt đất
            if (!m_IsGrounded)
            {
                m_MoveDirection *= m_AirControl;
            }
        }

        private void HandleJump()
        {
            if (m_JumpPressed && m_CanJump && m_IsGrounded)
            {
                // Tính toán jump velocity
                float jumpVelocity = Mathf.Sqrt(m_JumpHeight * -2f * m_Gravity);
                m_Velocity.y = jumpVelocity;
                m_CanJump = false;
                m_JumpPressed = false;
            }
        }

        private void UpdateGravity()
        {
            if (m_IsGrounded && m_Velocity.y < 0)
            {
                m_Velocity.y = -2f;
            }
            else
            {
                // Áp dụng gravity với fall multiplier cho faster falling
                float gravityMultiplier = 1f;
                
                if (m_Velocity.y < 0)
                {
                    gravityMultiplier = c_FallMultiplier;
                }
                else if (m_Velocity.y > 0 && !m_JumpPressed)
                {
                    gravityMultiplier = c_LowJumpMultiplier;
                }
                
                m_Velocity.y += m_Gravity * gravityMultiplier * Time.deltaTime;
            }
        }

        private void ApplyMovement()
        {
            // Kết hợp horizontal movement và vertical velocity
            Vector3 finalMovement = m_MoveDirection * m_CurrentSpeed * Time.deltaTime;
            finalMovement.y = m_Velocity.y * Time.deltaTime;
            
            // Áp dụng movement
            m_CharacterController.Move(finalMovement);
        }

        private void ToggleCrouch()
        {
            m_IsCrouching = !m_IsCrouching;
            
            // Điều chỉnh chiều cao CharacterController
            if (m_IsCrouching)
            {
                m_CharacterController.height = m_CrouchHeight;
                m_CharacterController.center = new Vector3(0, m_CrouchHeight * 0.5f, 0);
            }
            else
            {
                m_CharacterController.height = m_StandHeight;
                m_CharacterController.center = new Vector3(0, m_StandHeight * 0.5f, 0);
            }
        }

        private void DrawDebugGizmos()
        {
            if (m_GroundCheck != null)
            {
                // Vẽ ground check sphere
                Gizmos.color = m_IsGrounded ? Color.green : Color.red;
                Gizmos.DrawWireSphere(m_GroundCheck.position, c_GroundCheckDistance);
            }
            
            // Vẽ movement direction
            if (m_MoveDirection.magnitude > 0)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(transform.position, m_MoveDirection * 2f);
            }
        }
        #endregion

        #region Public Methods
        /// <summary>Thiết lập camera controller reference</summary>
        public void SetCameraController(CameraController _cameraController)
        {
            m_CameraController = _cameraController;
        }

        /// <summary>Lấy input movement hiện tại</summary>
        public Vector2 GetMoveInput()
        {
            return m_MoveInput;
        }

        /// <summary>Lấy move direction hiện tại</summary>
        public Vector3 GetMoveDirection()
        {
            return m_MoveDirection;
        }

        /// <summary>Kiểm tra xem có thể nhảy không</summary>
        public bool CanJump()
        {
            return m_CanJump && m_IsGrounded;
        }
        #endregion

        #if UNITY_EDITOR
        [ContextMenu("Reset Movement Settings")]
        private void ResetMovementSettings()
        {
            m_WalkSpeed = 5f;
            m_SprintSpeed = 8f;
            m_CrouchSpeed = 2f;
            m_JumpHeight = 2f;
            m_Gravity = -15f;
            Debug.Log("PlayerController: Movement settings đã được reset về mặc định.");
        }

        [ContextMenu("Debug Current State")]
        private void DebugCurrentState()
        {
            Debug.Log($"PlayerController State - Grounded: {m_IsGrounded}, Speed: {m_CurrentSpeed:F2}, " +
                     $"Sprinting: {m_IsSprinting}, Crouching: {m_IsCrouching}");
        }
        #endif
    }
}
