using UnityEngine;

namespace PlayerSystem
{
    /// <summary>
    /// Enum cho các loại interpolation của camera
    /// </summary>
    public enum CameraInterpolationType
    {
        None,           // Không có smoothing
        SmoothDamp,     // Sử dụng SmoothDamp (mặc định)
        Lerp,           // Sử dụng Lerp
        Slerp           // Sử dụng Slerp (cho rotation)
    }

    /// <summary>
    /// ScriptableObject chứa tất cả settings cho Player System
    /// C<PERSON> thể tạo nhiều preset khác nhau và dễ dàng điều chỉnh
    /// </summary>
    [CreateAssetMenu(fileName = "PlayerSettings", menuName = "Player System/Player Settings", order = 1)]
    public class PlayerSettings : ScriptableObject
    {
        #region Movement Settings
        [Header("Movement Settings")]
        [SerializeField, Range(1f, 10f), Tooltip("Tốc độ đi bộ cơ bản")]
        private float m_WalkSpeed = 5f;
        
        [SerializeField, Range(5f, 15f), Toolt<PERSON>("Tốc độ chạy nhanh")]
        private float m_SprintSpeed = 8f;
        
        [SerializeField, Range(0.5f, 5f), Tooltip("Tốc độ khi cúi người")]
        private float m_CrouchSpeed = 2f;
        
        [SerializeField, Range(5f, 20f), Tooltip("Tốc độ tăng tốc")]
        private float m_Acceleration = 10f;
        
        [SerializeField, Range(5f, 20f), Tooltip("Tốc độ giảm tốc")]
        private float m_Deceleration = 10f;
        
        [SerializeField, Range(0f, 1f), Tooltip("Khả năng điều khiển trong không khí")]
        private float m_AirControl = 0.3f;
        #endregion

        #region Jump Settings
        [Header("Jump Settings")]
        [SerializeField, Range(1f, 5f), Tooltip("Độ cao nhảy")]
        private float m_JumpHeight = 2f;
        
        [SerializeField, Range(-30f, -5f), Tooltip("Lực trọng lực")]
        private float m_Gravity = -15f;
        
        [SerializeField, Range(1f, 5f), Tooltip("Hệ số rơi nhanh")]
        private float m_FallMultiplier = 2.5f;
        
        [SerializeField, Range(1f, 5f), Tooltip("Hệ số nhảy thấp")]
        private float m_LowJumpMultiplier = 2f;
        #endregion

        #region Camera Settings
        [Header("Camera Settings")]
        [SerializeField, Range(50f, 500f), Tooltip("Độ nhạy chuột")]
        private float m_MouseSensitivity = 100f;

        [SerializeField, Range(50f, 500f), Tooltip("Độ nhạy gamepad")]
        private float m_GamepadSensitivity = 150f;

        [SerializeField, Tooltip("Đảo trục Y")]
        private bool m_InvertY = false;

        [SerializeField, Tooltip("Camera mượt mà")]
        private bool m_SmoothCamera = true;

        [SerializeField, Range(0.01f, 0.5f), Tooltip("Thời gian làm mượt camera")]
        private float m_SmoothTime = 0.1f;

        [Header("Advanced Camera Settings")]
        [SerializeField, Tooltip("Loại interpolation cho camera rotation")]
        private CameraInterpolationType m_InterpolationType = CameraInterpolationType.SmoothDamp;

        [SerializeField, Range(0.01f, 1f), Tooltip("Tốc độ interpolation cho Lerp/Slerp")]
        private float m_InterpolationSpeed = 0.15f;

        [SerializeField, Tooltip("Bật input smoothing cho mouse")]
        private bool m_EnableInputSmoothing = true;

        [SerializeField, Range(0.01f, 0.3f), Tooltip("Thời gian smooth input")]
        private float m_InputSmoothTime = 0.05f;

        [SerializeField, Range(0f, 5f), Tooltip("Mouse acceleration curve")]
        private float m_MouseAcceleration = 1f;

        [SerializeField, Range(0.1f, 3f), Tooltip("Multiplier cho high DPI mouse")]
        private float m_HighDPIMultiplier = 1f;

        [Header("Third Person Settings")]
        [SerializeField, Range(2f, 10f), Tooltip("Khoảng cách camera Third Person")]
        private float m_ThirdPersonDistance = 5f;

        [SerializeField, Tooltip("Offset camera Third Person")]
        private Vector3 m_ThirdPersonOffset = new Vector3(0, 2f, 0);
        
        [SerializeField, Tooltip("Offset camera First Person")]
        private Vector3 m_FirstPersonOffset = new Vector3(0, 0.6f, 0);
        
        [SerializeField, Range(1f, 10f), Tooltip("Tốc độ chuyển đổi camera")]
        private float m_CameraTransitionSpeed = 5f;
        #endregion

        #region Ground Check Settings
        [Header("Ground Check Settings")]
        [SerializeField, Range(0.1f, 1f), Tooltip("Khoảng cách kiểm tra mặt đất")]
        private float m_GroundCheckDistance = 0.4f;
        
        [SerializeField, Tooltip("Layer mask cho mặt đất")]
        private LayerMask m_GroundMask = 1;
        
        [SerializeField, Tooltip("Layer mask cho va chạm camera")]
        private LayerMask m_CameraCollisionLayers = 1;
        #endregion

        #region Crouch Settings
        [Header("Crouch Settings")]
        [SerializeField, Range(0.5f, 2f), Tooltip("Chiều cao khi cúi người")]
        private float m_CrouchHeight = 1f;
        
        [SerializeField, Range(1.5f, 3f), Tooltip("Chiều cao bình thường")]
        private float m_StandHeight = 2f;
        #endregion

        #region Input Settings
        [Header("Input Settings")]
        [SerializeField, Range(0f, 0.5f), Tooltip("Deadzone cho analog sticks")]
        private float m_AnalogDeadzone = 0.1f;
        
        [SerializeField, Range(0.5f, 3f), Tooltip("Multiplier cho gamepad sensitivity")]
        private float m_GamepadSensitivityMultiplier = 1.5f;
        #endregion

        #region Public Properties
        // Movement Properties
        public float WalkSpeed => m_WalkSpeed;
        public float SprintSpeed => m_SprintSpeed;
        public float CrouchSpeed => m_CrouchSpeed;
        public float Acceleration => m_Acceleration;
        public float Deceleration => m_Deceleration;
        public float AirControl => m_AirControl;

        // Jump Properties
        public float JumpHeight => m_JumpHeight;
        public float Gravity => m_Gravity;
        public float FallMultiplier => m_FallMultiplier;
        public float LowJumpMultiplier => m_LowJumpMultiplier;

        // Camera Properties
        public float MouseSensitivity => m_MouseSensitivity;
        public float GamepadSensitivity => m_GamepadSensitivity;
        public bool InvertY => m_InvertY;
        public bool SmoothCamera => m_SmoothCamera;
        public float SmoothTime => m_SmoothTime;
        public CameraInterpolationType InterpolationType => m_InterpolationType;
        public float InterpolationSpeed => m_InterpolationSpeed;
        public bool EnableInputSmoothing => m_EnableInputSmoothing;
        public float InputSmoothTime => m_InputSmoothTime;
        public float MouseAcceleration => m_MouseAcceleration;
        public float HighDPIMultiplier => m_HighDPIMultiplier;
        public float ThirdPersonDistance => m_ThirdPersonDistance;
        public Vector3 ThirdPersonOffset => m_ThirdPersonOffset;
        public Vector3 FirstPersonOffset => m_FirstPersonOffset;
        public float CameraTransitionSpeed => m_CameraTransitionSpeed;

        // Ground Check Properties
        public float GroundCheckDistance => m_GroundCheckDistance;
        public LayerMask GroundMask => m_GroundMask;
        public LayerMask CameraCollisionLayers => m_CameraCollisionLayers;

        // Crouch Properties
        public float CrouchHeight => m_CrouchHeight;
        public float StandHeight => m_StandHeight;

        // Input Properties
        public float AnalogDeadzone => m_AnalogDeadzone;
        public float GamepadSensitivityMultiplier => m_GamepadSensitivityMultiplier;
        #endregion

        #region Public Methods
        /// <summary>
        /// Validate tất cả settings để đảm bảo giá trị hợp lệ
        /// </summary>
        public void ValidateSettings()
        {
            // Movement validation
            m_WalkSpeed = Mathf.Max(0.1f, m_WalkSpeed);
            m_SprintSpeed = Mathf.Max(m_WalkSpeed, m_SprintSpeed);
            m_CrouchSpeed = Mathf.Max(0.1f, m_CrouchSpeed);
            m_Acceleration = Mathf.Max(0.1f, m_Acceleration);
            m_Deceleration = Mathf.Max(0.1f, m_Deceleration);
            m_AirControl = Mathf.Clamp01(m_AirControl);

            // Jump validation
            m_JumpHeight = Mathf.Max(0.1f, m_JumpHeight);
            m_Gravity = Mathf.Min(-0.1f, m_Gravity);
            m_FallMultiplier = Mathf.Max(1f, m_FallMultiplier);
            m_LowJumpMultiplier = Mathf.Max(1f, m_LowJumpMultiplier);

            // Camera validation
            m_MouseSensitivity = Mathf.Clamp(m_MouseSensitivity, 10f, 1000f);
            m_GamepadSensitivity = Mathf.Clamp(m_GamepadSensitivity, 10f, 1000f);
            m_SmoothTime = Mathf.Max(0.001f, m_SmoothTime);
            m_InterpolationSpeed = Mathf.Clamp01(m_InterpolationSpeed);
            m_InputSmoothTime = Mathf.Max(0.001f, m_InputSmoothTime);
            m_MouseAcceleration = Mathf.Max(0f, m_MouseAcceleration);
            m_HighDPIMultiplier = Mathf.Max(0.1f, m_HighDPIMultiplier);
            m_ThirdPersonDistance = Mathf.Max(1f, m_ThirdPersonDistance);
            m_CameraTransitionSpeed = Mathf.Max(0.1f, m_CameraTransitionSpeed);

            // Ground check validation
            m_GroundCheckDistance = Mathf.Max(0.01f, m_GroundCheckDistance);

            // Crouch validation
            m_CrouchHeight = Mathf.Max(0.1f, m_CrouchHeight);
            m_StandHeight = Mathf.Max(m_CrouchHeight + 0.1f, m_StandHeight);

            // Input validation
            m_AnalogDeadzone = Mathf.Clamp01(m_AnalogDeadzone);
            m_GamepadSensitivityMultiplier = Mathf.Max(0.1f, m_GamepadSensitivityMultiplier);
        }

        /// <summary>
        /// Reset tất cả settings về giá trị mặc định
        /// </summary>
        public void ResetToDefaults()
        {
            // Movement defaults
            m_WalkSpeed = 5f;
            m_SprintSpeed = 8f;
            m_CrouchSpeed = 2f;
            m_Acceleration = 10f;
            m_Deceleration = 10f;
            m_AirControl = 0.3f;

            // Jump defaults
            m_JumpHeight = 2f;
            m_Gravity = -15f;
            m_FallMultiplier = 2.5f;
            m_LowJumpMultiplier = 2f;

            // Camera defaults
            m_MouseSensitivity = 100f;
            m_GamepadSensitivity = 150f;
            m_InvertY = false;
            m_SmoothCamera = true;
            m_SmoothTime = 0.1f;
            m_InterpolationType = CameraInterpolationType.SmoothDamp;
            m_InterpolationSpeed = 0.15f;
            m_EnableInputSmoothing = true;
            m_InputSmoothTime = 0.05f;
            m_MouseAcceleration = 1f;
            m_HighDPIMultiplier = 1f;
            m_ThirdPersonDistance = 5f;
            m_ThirdPersonOffset = new Vector3(0, 2f, 0);
            m_FirstPersonOffset = new Vector3(0, 0.6f, 0);
            m_CameraTransitionSpeed = 5f;

            // Ground check defaults
            m_GroundCheckDistance = 0.4f;
            m_GroundMask = 1;
            m_CameraCollisionLayers = 1;

            // Crouch defaults
            m_CrouchHeight = 1f;
            m_StandHeight = 2f;

            // Input defaults
            m_AnalogDeadzone = 0.1f;
            m_GamepadSensitivityMultiplier = 1.5f;
        }

        /// <summary>
        /// Copy settings từ PlayerSettings khác
        /// </summary>
        public void CopyFrom(PlayerSettings _other)
        {
            if (_other == null) return;

            // Copy movement settings
            m_WalkSpeed = _other.m_WalkSpeed;
            m_SprintSpeed = _other.m_SprintSpeed;
            m_CrouchSpeed = _other.m_CrouchSpeed;
            m_Acceleration = _other.m_Acceleration;
            m_Deceleration = _other.m_Deceleration;
            m_AirControl = _other.m_AirControl;

            // Copy jump settings
            m_JumpHeight = _other.m_JumpHeight;
            m_Gravity = _other.m_Gravity;
            m_FallMultiplier = _other.m_FallMultiplier;
            m_LowJumpMultiplier = _other.m_LowJumpMultiplier;

            // Copy camera settings
            m_MouseSensitivity = _other.m_MouseSensitivity;
            m_GamepadSensitivity = _other.m_GamepadSensitivity;
            m_InvertY = _other.m_InvertY;
            m_SmoothCamera = _other.m_SmoothCamera;
            m_SmoothTime = _other.m_SmoothTime;
            m_ThirdPersonDistance = _other.m_ThirdPersonDistance;
            m_ThirdPersonOffset = _other.m_ThirdPersonOffset;
            m_FirstPersonOffset = _other.m_FirstPersonOffset;
            m_CameraTransitionSpeed = _other.m_CameraTransitionSpeed;

            // Copy ground check settings
            m_GroundCheckDistance = _other.m_GroundCheckDistance;
            m_GroundMask = _other.m_GroundMask;
            m_CameraCollisionLayers = _other.m_CameraCollisionLayers;

            // Copy crouch settings
            m_CrouchHeight = _other.m_CrouchHeight;
            m_StandHeight = _other.m_StandHeight;

            // Copy input settings
            m_AnalogDeadzone = _other.m_AnalogDeadzone;
            m_GamepadSensitivityMultiplier = _other.m_GamepadSensitivityMultiplier;
        }
        #endregion

        #if UNITY_EDITOR
        [ContextMenu("Validate All Settings")]
        private void ValidateAllSettings()
        {
            ValidateSettings();
            UnityEditor.EditorUtility.SetDirty(this);
            Debug.Log("PlayerSettings: Tất cả settings đã được validate.");
        }

        [ContextMenu("Reset to Defaults")]
        private void ResetAllToDefaults()
        {
            ResetToDefaults();
            UnityEditor.EditorUtility.SetDirty(this);
            Debug.Log("PlayerSettings: Đã reset về giá trị mặc định.");
        }

        private void OnValidate()
        {
            ValidateSettings();
        }
        #endif
    }
}
