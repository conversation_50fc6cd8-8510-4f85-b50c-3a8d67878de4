using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System;

namespace EconomySystem
{
    /// <summary>
    /// Máy mua/bán tự động với trigger zones và UI chỉnh giá
    /// Hỗ trợ cả mua và bán vật phẩm với giá có thể điều chỉnh
    /// </summary>
    public class TradingMachine : MonoBehaviour
    {
        #region Enums
        public enum TradingMode
        {
            Buy,    // Máy mua (người chơi bán)
            Sell,   // M<PERSON><PERSON> bán (người chơi mua)
            Both    // Cả hai
        }
        #endregion

        #region Serialized Fields
        [Header("🏪 Cài Đặt Máy")]
        [SerializeField] private TradingMode m_LoaiMay = TradingMode.Both;
        [SerializeField] private string m_TenMay = "Máy Giao Dịch";
        [SerializeField] private string m_ItemId = "wood";
        [SerializeField] private bool m_HienThiLog = true;

        [Header("💰 Cài Đặt Giá")]
        [SerializeField] private int m_GiaMua = 10;      // <PERSON>i<PERSON> máy mua từ người chơi
        [SerializeField] private int m_GiaBan = 15;      // Giá máy bán cho người chơi
        [SerializeField] private int m_GiaToiThieu = 1;
        [SerializeField] private int m_GiaToiDa = 1000;
        [SerializeField] private bool m_ChoPhepChinhGia = true;

        [Header("📦 Cài Đặt Số Lượng")]
        [SerializeField] private int m_SoLuongToiThieu = 1;
        [SerializeField] private int m_SoLuongToiDa = 99;
        [SerializeField] private int m_SoLuongMacDinh = 1;

        [Header("🎯 Trigger Zone")]
        [SerializeField] private Collider m_TriggerZone;
        [SerializeField] private LayerMask m_PlayerLayer = -1;
        [SerializeField] private float m_KhoangCachTuongTac = 3f;

        [Header("🎨 UI References")]
        [SerializeField] private Canvas m_UICanvas;
        [SerializeField] private GameObject m_PanelGiaoDich;
        [SerializeField] private GameObject m_PanelChinhGia;
        [SerializeField] private TextMeshProUGUI m_TextTenMay;
        [SerializeField] private TextMeshProUGUI m_TextItemName;
        
        // Trading UI
        [SerializeField] private Button m_ButtonMua;
        [SerializeField] private Button m_ButtonBan;
        [SerializeField] private Button m_ButtonDong;
        [SerializeField] private Slider m_SliderSoLuong;
        [SerializeField] private TextMeshProUGUI m_TextSoLuong;
        [SerializeField] private TextMeshProUGUI m_TextGiaMua;
        [SerializeField] private TextMeshProUGUI m_TextGiaBan;
        [SerializeField] private TextMeshProUGUI m_TextTongTien;

        // Price Adjustment UI
        [SerializeField] private Button m_ButtonChinhGia;
        [SerializeField] private Slider m_SliderGiaMua;
        [SerializeField] private Slider m_SliderGiaBan;
        [SerializeField] private TMP_InputField m_InputGiaMua;
        [SerializeField] private TMP_InputField m_InputGiaBan;
        [SerializeField] private Button m_ButtonLuuGia;
        [SerializeField] private Button m_ButtonHuyChinhGia;

        [Header("🔊 Audio")]
        [SerializeField] private AudioSource m_AudioSource;
        [SerializeField] private AudioClip m_SoundMua;
        [SerializeField] private AudioClip m_SoundBan;
        [SerializeField] private AudioClip m_SoundLoi;

        [Header("✨ Effects")]
        [SerializeField] private ParticleSystem m_EffectMua;
        [SerializeField] private ParticleSystem m_EffectBan;
        [SerializeField] private GameObject m_IndicatorMua;
        [SerializeField] private GameObject m_IndicatorBan;
        #endregion

        #region Private Fields
        private EconomySystemManager m_EconomySystem;
        private bool m_PlayerTrongZone = false;
        private bool m_UIOpen = false;
        private bool m_ChinhGiaMode = false;
        private int m_SoLuongHienTai = 1;
        #endregion

        #region Events
        public static event Action<TradingMachine, string, int, int> OnItemTraded; // machine, itemId, quantity, price
        public static event Action<TradingMachine, int, int> OnPriceChanged; // machine, buyPrice, sellPrice
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            ThietLapComponents();
        }

        private void Start()
        {
            TimEconomySystem();
            ThietLapUI();
            ThietLapTriggerZone();
            CapNhatIndicators();
        }

        private void Update()
        {
            XuLyInput();
        }

        private void OnTriggerEnter(Collider other)
        {
            if (KiemTraPlayer(other))
            {
                m_PlayerTrongZone = true;
                HienThiPrompt(true);
                Log($"Player vào zone: {m_TenMay}");
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (KiemTraPlayer(other))
            {
                m_PlayerTrongZone = false;
                HienThiPrompt(false);
                DongUI();
                Log($"Player rời zone: {m_TenMay}");
            }
        }
        #endregion

        #region Setup Methods
        private void ThietLapComponents()
        {
            if (m_TriggerZone == null)
                m_TriggerZone = GetComponent<Collider>();

            if (m_AudioSource == null)
                m_AudioSource = GetComponent<AudioSource>();

            if (m_UICanvas == null)
                m_UICanvas = GetComponentInChildren<Canvas>();
        }

        private void ThietLapUI()
        {
            if (m_UICanvas != null)
                m_UICanvas.gameObject.SetActive(false);

            // Setup button events
            if (m_ButtonMua != null)
                m_ButtonMua.onClick.AddListener(XuLyMua);

            if (m_ButtonBan != null)
                m_ButtonBan.onClick.AddListener(XuLyBan);

            if (m_ButtonDong != null)
                m_ButtonDong.onClick.AddListener(DongUI);

            if (m_ButtonChinhGia != null)
                m_ButtonChinhGia.onClick.AddListener(MoChinhGia);

            if (m_ButtonLuuGia != null)
                m_ButtonLuuGia.onClick.AddListener(LuuGia);

            if (m_ButtonHuyChinhGia != null)
                m_ButtonHuyChinhGia.onClick.AddListener(HuyChinhGia);

            // Setup sliders
            if (m_SliderSoLuong != null)
            {
                m_SliderSoLuong.minValue = m_SoLuongToiThieu;
                m_SliderSoLuong.maxValue = m_SoLuongToiDa;
                m_SliderSoLuong.value = m_SoLuongMacDinh;
                m_SliderSoLuong.onValueChanged.AddListener(OnSoLuongChanged);
            }

            if (m_SliderGiaMua != null)
            {
                m_SliderGiaMua.minValue = m_GiaToiThieu;
                m_SliderGiaMua.maxValue = m_GiaToiDa;
                m_SliderGiaMua.value = m_GiaMua;
                m_SliderGiaMua.onValueChanged.AddListener(OnGiaMuaSliderChanged);
            }

            if (m_SliderGiaBan != null)
            {
                m_SliderGiaBan.minValue = m_GiaToiThieu;
                m_SliderGiaBan.maxValue = m_GiaToiDa;
                m_SliderGiaBan.value = m_GiaBan;
                m_SliderGiaBan.onValueChanged.AddListener(OnGiaBanSliderChanged);
            }

            // Setup input fields
            if (m_InputGiaMua != null)
                m_InputGiaMua.onValueChanged.AddListener(OnGiaMuaInputChanged);

            if (m_InputGiaBan != null)
                m_InputGiaBan.onValueChanged.AddListener(OnGiaBanInputChanged);

            CapNhatUIText();
        }

        private void ThietLapTriggerZone()
        {
            if (m_TriggerZone != null)
            {
                m_TriggerZone.isTrigger = true;
                Log($"Thiết lập trigger zone cho {m_TenMay}");
            }
        }

        private void TimEconomySystem()
        {
            m_EconomySystem = FindObjectOfType<EconomySystemManager>();
            if (m_EconomySystem == null)
            {
                LogLoi("Không tìm thấy EconomySystemManager!");
            }
        }
        #endregion

        #region Input Handling
        private void XuLyInput()
        {
            if (m_PlayerTrongZone && Input.GetKeyDown(KeyCode.F))
            {
                if (!m_UIOpen)
                    MoUI();
                else
                    DongUI();
            }

            if (m_UIOpen && Input.GetKeyDown(KeyCode.Escape))
            {
                DongUI();
            }
        }

        private bool KiemTraPlayer(Collider other)
        {
            return ((1 << other.gameObject.layer) & m_PlayerLayer) != 0;
        }
        #endregion

        #region UI Management
        public void MoUI()
        {
            if (m_EconomySystem == null || !m_EconomySystem.HeThongSanSang)
            {
                LogLoi("Hệ thống Economy chưa sẵn sàng!");
                return;
            }

            m_UIOpen = true;
            if (m_UICanvas != null)
                m_UICanvas.gameObject.SetActive(true);

            if (m_PanelGiaoDich != null)
                m_PanelGiaoDich.SetActive(true);

            if (m_PanelChinhGia != null)
                m_PanelChinhGia.SetActive(false);

            CapNhatUIText();
            CapNhatButtonStates();
            Log($"Mở UI: {m_TenMay}");
        }

        public void DongUI()
        {
            m_UIOpen = false;
            m_ChinhGiaMode = false;

            if (m_UICanvas != null)
                m_UICanvas.gameObject.SetActive(false);

            Log($"Đóng UI: {m_TenMay}");
        }

        private void HienThiPrompt(bool hienThi)
        {
            // Có thể hiển thị prompt "Nhấn F để tương tác" ở đây
            if (hienThi)
            {
                Log($"Hiển thị prompt cho {m_TenMay}");
            }
        }

        private void CapNhatUIText()
        {
            if (m_TextTenMay != null)
                m_TextTenMay.text = m_TenMay;

            if (m_TextItemName != null)
                m_TextItemName.text = m_ItemId;

            if (m_TextSoLuong != null)
                m_TextSoLuong.text = m_SoLuongHienTai.ToString();

            if (m_TextGiaMua != null)
                m_TextGiaMua.text = $"Giá mua: {m_GiaMua} Lea";

            if (m_TextGiaBan != null)
                m_TextGiaBan.text = $"Giá bán: {m_GiaBan} Lea";

            CapNhatTongTien();
        }

        private void CapNhatTongTien()
        {
            if (m_TextTongTien != null)
            {
                int tongMua = m_GiaMua * m_SoLuongHienTai;
                int tongBan = m_GiaBan * m_SoLuongHienTai;
                m_TextTongTien.text = $"Mua: {tongMua} Lea | Bán: {tongBan} Lea";
            }
        }

        private void CapNhatButtonStates()
        {
            if (m_EconomySystem == null) return;

            // Cập nhật button mua (người chơi mua từ máy)
            if (m_ButtonMua != null)
            {
                bool coTheMua = m_LoaiMay == TradingMode.Sell || m_LoaiMay == TradingMode.Both;
                bool duTien = m_EconomySystem.CurrencyManager.CoTheMua(m_GiaBan * m_SoLuongHienTai);
                bool khoKhongDay = !m_EconomySystem.PlayerInventory.KhoDay || 
                                  m_EconomySystem.PlayerInventory.CoVatPham(m_ItemId);
                
                m_ButtonMua.interactable = coTheMua && duTien && khoKhongDay;
            }

            // Cập nhật button bán (người chơi bán cho máy)
            if (m_ButtonBan != null)
            {
                bool coTheBan = m_LoaiMay == TradingMode.Buy || m_LoaiMay == TradingMode.Both;
                bool coVatPham = m_EconomySystem.PlayerInventory.CoVatPham(m_ItemId, m_SoLuongHienTai);
                
                m_ButtonBan.interactable = coTheBan && coVatPham;
            }

            // Cập nhật button chỉnh giá
            if (m_ButtonChinhGia != null)
            {
                m_ButtonChinhGia.interactable = m_ChoPhepChinhGia;
            }
        }

        private void CapNhatIndicators()
        {
            if (m_IndicatorMua != null)
                m_IndicatorMua.SetActive(m_LoaiMay == TradingMode.Buy || m_LoaiMay == TradingMode.Both);

            if (m_IndicatorBan != null)
                m_IndicatorBan.SetActive(m_LoaiMay == TradingMode.Sell || m_LoaiMay == TradingMode.Both);
        }
        #endregion

        #region Trading Operations
        private void XuLyMua()
        {
            if (m_EconomySystem == null) return;

            int tongTien = m_GiaBan * m_SoLuongHienTai;
            
            if (m_EconomySystem.MuaVatPham(m_ItemId, m_SoLuongHienTai, m_GiaBan))
            {
                Log($"Mua thành công: {m_SoLuongHienTai} {m_ItemId} với {tongTien} Lea");
                
                // Effects
                if (m_EffectMua != null)
                    m_EffectMua.Play();
                
                if (m_AudioSource != null && m_SoundMua != null)
                    m_AudioSource.PlayOneShot(m_SoundMua);

                OnItemTraded?.Invoke(this, m_ItemId, m_SoLuongHienTai, m_GiaBan);
                CapNhatButtonStates();
            }
            else
            {
                LogLoi("Không thể mua vật phẩm!");
                
                if (m_AudioSource != null && m_SoundLoi != null)
                    m_AudioSource.PlayOneShot(m_SoundLoi);
            }
        }

        private void XuLyBan()
        {
            if (m_EconomySystem == null) return;

            int tongTien = m_GiaMua * m_SoLuongHienTai;
            
            if (m_EconomySystem.BanVatPham(m_ItemId, m_SoLuongHienTai, m_GiaMua))
            {
                Log($"Bán thành công: {m_SoLuongHienTai} {m_ItemId} nhận {tongTien} Lea");
                
                // Effects
                if (m_EffectBan != null)
                    m_EffectBan.Play();
                
                if (m_AudioSource != null && m_SoundBan != null)
                    m_AudioSource.PlayOneShot(m_SoundBan);

                OnItemTraded?.Invoke(this, m_ItemId, -m_SoLuongHienTai, m_GiaMua);
                CapNhatButtonStates();
            }
            else
            {
                LogLoi("Không thể bán vật phẩm!");
                
                if (m_AudioSource != null && m_SoundLoi != null)
                    m_AudioSource.PlayOneShot(m_SoundLoi);
            }
        }
        #endregion

        #region Price Adjustment
        private void MoChinhGia()
        {
            if (!m_ChoPhepChinhGia) return;

            m_ChinhGiaMode = true;
            
            if (m_PanelGiaoDich != null)
                m_PanelGiaoDich.SetActive(false);

            if (m_PanelChinhGia != null)
                m_PanelChinhGia.SetActive(true);

            // Cập nhật UI chỉnh giá
            if (m_SliderGiaMua != null)
                m_SliderGiaMua.value = m_GiaMua;

            if (m_SliderGiaBan != null)
                m_SliderGiaBan.value = m_GiaBan;

            if (m_InputGiaMua != null)
                m_InputGiaMua.text = m_GiaMua.ToString();

            if (m_InputGiaBan != null)
                m_InputGiaBan.text = m_GiaBan.ToString();

            Log("Mở chế độ chỉnh giá");
        }

        private void LuuGia()
        {
            int giaMuaMoi = Mathf.Clamp((int)m_SliderGiaMua.value, m_GiaToiThieu, m_GiaToiDa);
            int giaBanMoi = Mathf.Clamp((int)m_SliderGiaBan.value, m_GiaToiThieu, m_GiaToiDa);

            m_GiaMua = giaMuaMoi;
            m_GiaBan = giaBanMoi;

            OnPriceChanged?.Invoke(this, m_GiaMua, m_GiaBan);
            
            HuyChinhGia();
            CapNhatUIText();
            
            Log($"Lưu giá mới - Mua: {m_GiaMua}, Bán: {m_GiaBan}");
        }

        private void HuyChinhGia()
        {
            m_ChinhGiaMode = false;
            
            if (m_PanelChinhGia != null)
                m_PanelChinhGia.SetActive(false);

            if (m_PanelGiaoDich != null)
                m_PanelGiaoDich.SetActive(true);

            Log("Hủy chỉnh giá");
        }
        #endregion

        #region UI Event Handlers
        private void OnSoLuongChanged(float value)
        {
            m_SoLuongHienTai = (int)value;
            CapNhatUIText();
            CapNhatButtonStates();
        }

        private void OnGiaMuaSliderChanged(float value)
        {
            if (m_InputGiaMua != null)
                m_InputGiaMua.text = ((int)value).ToString();
        }

        private void OnGiaBanSliderChanged(float value)
        {
            if (m_InputGiaBan != null)
                m_InputGiaBan.text = ((int)value).ToString();
        }

        private void OnGiaMuaInputChanged(string value)
        {
            if (int.TryParse(value, out int gia))
            {
                gia = Mathf.Clamp(gia, m_GiaToiThieu, m_GiaToiDa);
                if (m_SliderGiaMua != null)
                    m_SliderGiaMua.value = gia;
            }
        }

        private void OnGiaBanInputChanged(string value)
        {
            if (int.TryParse(value, out int gia))
            {
                gia = Mathf.Clamp(gia, m_GiaToiThieu, m_GiaToiDa);
                if (m_SliderGiaBan != null)
                    m_SliderGiaBan.value = gia;
            }
        }
        #endregion

        #region Public Methods
        public void DatGia(int giaMua, int giaBan)
        {
            m_GiaMua = Mathf.Clamp(giaMua, m_GiaToiThieu, m_GiaToiDa);
            m_GiaBan = Mathf.Clamp(giaBan, m_GiaToiThieu, m_GiaToiDa);
            CapNhatUIText();
            Log($"Đặt giá - Mua: {m_GiaMua}, Bán: {m_GiaBan}");
        }

        public void DatItemId(string itemId)
        {
            m_ItemId = itemId;
            CapNhatUIText();
            Log($"Đặt item ID: {m_ItemId}");
        }

        public void DatLoaiMay(TradingMode loaiMay)
        {
            m_LoaiMay = loaiMay;
            CapNhatIndicators();
            CapNhatButtonStates();
            Log($"Đặt loại máy: {m_LoaiMay}");
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[TradingMachine-{m_TenMay}] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[TradingMachine-{m_TenMay}] {message}");
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Test Mở UI")]
        private void TestMoUI()
        {
            MoUI();
        }

        [ContextMenu("Test Đóng UI")]
        private void TestDongUI()
        {
            DongUI();
        }

        [ContextMenu("Hiển Thị Thông Tin")]
        private void HienThiThongTin()
        {
            Debug.Log($"=== THÔNG TIN TRADING MACHINE ===\n" +
                     $"Tên: {m_TenMay}\n" +
                     $"Loại: {m_LoaiMay}\n" +
                     $"Item: {m_ItemId}\n" +
                     $"Giá mua: {m_GiaMua} Lea\n" +
                     $"Giá bán: {m_GiaBan} Lea\n" +
                     $"Player trong zone: {m_PlayerTrongZone}\n" +
                     $"UI mở: {m_UIOpen}");
        }
        #endif
        #endregion
    }
}
