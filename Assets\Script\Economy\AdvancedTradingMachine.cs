using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System;

namespace EconomySystem
{
    /// <summary>
    /// M<PERSON>y bán hàng nâng cấp với trigger zones cho objects
    /// Hỗ trợ đặt object vào vùng để bán/mua
    /// </summary>
    public class AdvancedTradingMachine : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🏪 Cài Đặt Máy")]
        [SerializeField] private string m_TenMay = "Máy Giao Dịch Nâng Cấp";
        [SerializeField] private bool m_HienThiLog = true;

        [Header("🎯 Trigger Zones")]
        [SerializeField] private Transform m_SellZone;        // Vùng đặt object để bán
        [SerializeField] private Transform m_BuyZone;         // Vùng đặt object để mua
        [SerializeField] private LayerMask m_ItemLayer = -1;  // Layer của items
        [SerializeField] private LayerMask m_PlayerLayer = -1; // Layer của player
        [SerializeField] private float m_KhoangCachTuongTac = 5f;

        [Header("💰 Cài Đặt Giá")]
        [SerializeField] private float m_PhanTramGiamGiaBan = 0.7f; // Bán được 70% giá gốc
        [SerializeField] private float m_PhanTramTangGiaMua = 1.2f; // Mua tốn 120% giá gốc

        [Header("🎨 UI References")]
        [SerializeField] private Canvas m_UICanvas;
        [SerializeField] private GameObject m_PanelBan;       // Panel khi đặt object vào sell zone
        [SerializeField] private GameObject m_PanelMua;       // Panel khi đặt object vào buy zone
        [SerializeField] private GameObject m_PanelPrompt;    // Panel hướng dẫn

        // Sell Panel UI
        [SerializeField] private TextMeshProUGUI m_TextItemBan;
        [SerializeField] private TextMeshProUGUI m_TextGiaBan;
        [SerializeField] private Button m_ButtonXacNhanBan;
        [SerializeField] private Button m_ButtonHuyBan;

        // Buy Panel UI  
        [SerializeField] private TextMeshProUGUI m_TextItemMua;
        [SerializeField] private TextMeshProUGUI m_TextGiaMua;
        [SerializeField] private Button m_ButtonXacNhanMua;
        [SerializeField] private Button m_ButtonHuyMua;

        // Prompt UI
        [SerializeField] private TextMeshProUGUI m_TextPrompt;

        [Header("✨ Effects")]
        [SerializeField] private ParticleSystem m_EffectBan;
        [SerializeField] private ParticleSystem m_EffectMua;
        [SerializeField] private AudioSource m_AudioSource;
        [SerializeField] private AudioClip m_SoundBan;
        [SerializeField] private AudioClip m_SoundMua;
        [SerializeField] private AudioClip m_SoundLoi;

        [Header("🔧 Visual Indicators")]
        [SerializeField] private GameObject m_IndicatorSellZone;
        [SerializeField] private GameObject m_IndicatorBuyZone;
        [SerializeField] private Material m_MaterialSellZone;
        [SerializeField] private Material m_MaterialBuyZone;
        #endregion

        #region Private Fields
        private EconomySystemManager m_EconomySystem;
        private bool m_PlayerTrongZone = false;
        private GameObject m_ObjectTrongSellZone = null;
        private GameObject m_ObjectTrongBuyZone = null;
        private TradableItem m_ItemDangBan = null;
        private TradableItem m_ItemDangMua = null;
        private List<TradableItem> m_DanhSachItemCoTheMua = new List<TradableItem>();
        #endregion

        #region Events
        public static event Action<AdvancedTradingMachine, string, int> OnItemSold;
        public static event Action<AdvancedTradingMachine, string, int> OnItemBought;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            TimEconomySystem();
            ThietLapUI();
            ThietLapTriggerZones();
            TaiDanhSachItemCoTheMua();
        }

        private void Update()
        {
            KiemTraPlayerTrongZone();
            CapNhatUI();
        }
        #endregion

        #region Setup Methods
        private void TimEconomySystem()
        {
            m_EconomySystem = FindObjectOfType<EconomySystemManager>();
            if (m_EconomySystem == null)
            {
                LogLoi("Không tìm thấy EconomySystemManager!");
            }
        }

        private void ThietLapUI()
        {
            if (m_UICanvas != null)
                m_UICanvas.gameObject.SetActive(false);

            // Setup button events
            if (m_ButtonXacNhanBan != null)
                m_ButtonXacNhanBan.onClick.AddListener(XacNhanBan);

            if (m_ButtonHuyBan != null)
                m_ButtonHuyBan.onClick.AddListener(HuyBan);

            if (m_ButtonXacNhanMua != null)
                m_ButtonXacNhanMua.onClick.AddListener(XacNhanMua);

            if (m_ButtonHuyMua != null)
                m_ButtonHuyMua.onClick.AddListener(HuyMua);

            // Ẩn tất cả panels
            if (m_PanelBan != null) m_PanelBan.SetActive(false);
            if (m_PanelMua != null) m_PanelMua.SetActive(false);
            if (m_PanelPrompt != null) m_PanelPrompt.SetActive(false);
        }

        private void ThietLapTriggerZones()
        {
            // Thiết lập Sell Zone
            if (m_SellZone != null)
            {
                var sellTrigger = m_SellZone.GetComponent<Collider>();
                if (sellTrigger == null)
                {
                    sellTrigger = m_SellZone.gameObject.AddComponent<BoxCollider>();
                }
                sellTrigger.isTrigger = true;

                var sellZoneDetector = m_SellZone.GetComponent<TriggerZoneDetector>();
                if (sellZoneDetector == null)
                {
                    sellZoneDetector = m_SellZone.gameObject.AddComponent<TriggerZoneDetector>();
                }
                sellZoneDetector.OnObjectEnter += OnObjectEnterSellZone;
                sellZoneDetector.OnObjectExit += OnObjectExitSellZone;
            }

            // Thiết lập Buy Zone
            if (m_BuyZone != null)
            {
                var buyTrigger = m_BuyZone.GetComponent<Collider>();
                if (buyTrigger == null)
                {
                    buyTrigger = m_BuyZone.gameObject.AddComponent<BoxCollider>();
                }
                buyTrigger.isTrigger = true;

                var buyZoneDetector = m_BuyZone.GetComponent<TriggerZoneDetector>();
                if (buyZoneDetector == null)
                {
                    buyZoneDetector = m_BuyZone.gameObject.AddComponent<TriggerZoneDetector>();
                }
                buyZoneDetector.OnObjectEnter += OnObjectEnterBuyZone;
                buyZoneDetector.OnObjectExit += OnObjectExitBuyZone;
            }
        }

        private void TaiDanhSachItemCoTheMua()
        {
            // Tạo danh sách items có thể mua từ ItemDatabase hoặc cấu hình sẵn
            m_DanhSachItemCoTheMua.Clear();
            
            // Thêm một số items mẫu
            m_DanhSachItemCoTheMua.Add(new TradableItem("wood", "Gỗ", 10));
            m_DanhSachItemCoTheMua.Add(new TradableItem("stone", "Đá", 15));
            m_DanhSachItemCoTheMua.Add(new TradableItem("sword", "Kiếm", 100));
            m_DanhSachItemCoTheMua.Add(new TradableItem("shield", "Khiên", 80));
            
            Log($"Tải {m_DanhSachItemCoTheMua.Count} items có thể mua");
        }
        #endregion

        #region Zone Detection
        private void KiemTraPlayerTrongZone()
        {
            bool playerTrongZoneTruoc = m_PlayerTrongZone;
            m_PlayerTrongZone = false;

            // Kiểm tra player có trong khoảng cách tương tác không
            Collider[] players = Physics.OverlapSphere(transform.position, m_KhoangCachTuongTac, m_PlayerLayer);
            m_PlayerTrongZone = players.Length > 0;

            if (m_PlayerTrongZone != playerTrongZoneTruoc)
            {
                if (m_PlayerTrongZone)
                {
                    HienThiPrompt();
                    Log("Player vào zone");
                }
                else
                {
                    AnTatCaUI();
                    Log("Player rời zone");
                }
            }
        }

        private void OnObjectEnterSellZone(GameObject obj)
        {
            var tradableItem = obj.GetComponent<TradableItem>();
            if (tradableItem != null && m_ObjectTrongSellZone == null)
            {
                m_ObjectTrongSellZone = obj;
                m_ItemDangBan = tradableItem;
                HienThiPanelBan();
                Log($"Object vào sell zone: {tradableItem.ItemName}");
            }
        }

        private void OnObjectExitSellZone(GameObject obj)
        {
            if (obj == m_ObjectTrongSellZone)
            {
                m_ObjectTrongSellZone = null;
                m_ItemDangBan = null;
                AnPanelBan();
                Log("Object rời sell zone");
            }
        }

        private void OnObjectEnterBuyZone(GameObject obj)
        {
            // Kiểm tra xem object này có phải là "buy token" không
            var buyToken = obj.GetComponent<BuyToken>();
            if (buyToken != null && m_ObjectTrongBuyZone == null)
            {
                m_ObjectTrongBuyZone = obj;
                
                // Tìm item tương ứng trong danh sách
                var item = m_DanhSachItemCoTheMua.Find(x => x.ItemId == buyToken.ItemId);
                if (item != null)
                {
                    m_ItemDangMua = item;
                    HienThiPanelMua();
                    Log($"Buy token vào buy zone: {item.ItemName}");
                }
            }
        }

        private void OnObjectExitBuyZone(GameObject obj)
        {
            if (obj == m_ObjectTrongBuyZone)
            {
                m_ObjectTrongBuyZone = null;
                m_ItemDangMua = null;
                AnPanelMua();
                Log("Buy token rời buy zone");
            }
        }
        #endregion

        #region UI Management
        private void HienThiPrompt()
        {
            if (m_PanelPrompt != null && m_PlayerTrongZone)
            {
                m_UICanvas?.gameObject.SetActive(true);
                m_PanelPrompt.SetActive(true);
                
                if (m_TextPrompt != null)
                {
                    string prompt = "🏪 " + m_TenMay + "\n";
                    prompt += "📦 Đặt vật phẩm vào vùng VÀNG để BÁN\n";
                    prompt += "🛒 Đặt token vào vùng XANH để MUA";
                    m_TextPrompt.text = prompt;
                }
            }
        }

        private void HienThiPanelBan()
        {
            if (m_PanelBan != null && m_ItemDangBan != null && m_PlayerTrongZone)
            {
                m_UICanvas?.gameObject.SetActive(true);
                m_PanelBan.SetActive(true);
                m_PanelPrompt?.SetActive(false);

                int giaBan = Mathf.RoundToInt(m_ItemDangBan.BasePrice * m_PhanTramGiamGiaBan);

                if (m_TextItemBan != null)
                    m_TextItemBan.text = $"Bán: {m_ItemDangBan.ItemName}";

                if (m_TextGiaBan != null)
                    m_TextGiaBan.text = $"Giá: {giaBan} Lea";

                Log($"Hiển thị panel bán: {m_ItemDangBan.ItemName} - {giaBan} Lea");
            }
        }

        private void HienThiPanelMua()
        {
            if (m_PanelMua != null && m_ItemDangMua != null && m_PlayerTrongZone)
            {
                m_UICanvas?.gameObject.SetActive(true);
                m_PanelMua.SetActive(true);
                m_PanelPrompt?.SetActive(false);

                int giaMua = Mathf.RoundToInt(m_ItemDangMua.BasePrice * m_PhanTramTangGiaMua);

                if (m_TextItemMua != null)
                    m_TextItemMua.text = $"Mua: {m_ItemDangMua.ItemName}";

                if (m_TextGiaMua != null)
                    m_TextGiaMua.text = $"Giá: {giaMua} Lea";

                // Kiểm tra đủ tiền
                bool duTien = m_EconomySystem?.CurrencyManager.CoTheMua(giaMua) ?? false;
                if (m_ButtonXacNhanMua != null)
                    m_ButtonXacNhanMua.interactable = duTien;

                Log($"Hiển thị panel mua: {m_ItemDangMua.ItemName} - {giaMua} Lea");
            }
        }

        private void AnPanelBan()
        {
            if (m_PanelBan != null)
                m_PanelBan.SetActive(false);
            
            if (m_PlayerTrongZone)
                HienThiPrompt();
        }

        private void AnPanelMua()
        {
            if (m_PanelMua != null)
                m_PanelMua.SetActive(false);
            
            if (m_PlayerTrongZone)
                HienThiPrompt();
        }

        private void AnTatCaUI()
        {
            if (m_UICanvas != null)
                m_UICanvas.gameObject.SetActive(false);
        }

        private void CapNhatUI()
        {
            // Cập nhật màu sắc indicators
            if (m_IndicatorSellZone != null)
            {
                var renderer = m_IndicatorSellZone.GetComponent<Renderer>();
                if (renderer != null && m_MaterialSellZone != null)
                {
                    Color color = m_ObjectTrongSellZone != null ? Color.green : Color.yellow;
                    m_MaterialSellZone.color = color;
                }
            }

            if (m_IndicatorBuyZone != null)
            {
                var renderer = m_IndicatorBuyZone.GetComponent<Renderer>();
                if (renderer != null && m_MaterialBuyZone != null)
                {
                    Color color = m_ObjectTrongBuyZone != null ? Color.green : Color.cyan;
                    m_MaterialBuyZone.color = color;
                }
            }
        }
        #endregion

        #region Trading Operations
        private void XacNhanBan()
        {
            if (m_ItemDangBan == null || m_EconomySystem == null)
            {
                LogLoi("Không thể bán - thiếu thông tin!");
                return;
            }

            int giaBan = Mathf.RoundToInt(m_ItemDangBan.BasePrice * m_PhanTramGiamGiaBan);
            
            // Thêm tiền cho player
            if (m_EconomySystem.CurrencyManager.ThemTien(giaBan))
            {
                // Xóa object khỏi scene
                if (m_ObjectTrongSellZone != null)
                {
                    Destroy(m_ObjectTrongSellZone);
                    m_ObjectTrongSellZone = null;
                }

                // Effects
                if (m_EffectBan != null)
                    m_EffectBan.Play();

                if (m_AudioSource != null && m_SoundBan != null)
                    m_AudioSource.PlayOneShot(m_SoundBan);

                OnItemSold?.Invoke(this, m_ItemDangBan.ItemId, giaBan);
                Log($"Bán thành công: {m_ItemDangBan.ItemName} - Nhận {giaBan} Lea");

                m_ItemDangBan = null;
                AnPanelBan();
            }
            else
            {
                LogLoi("Không thể thêm tiền!");
                PlayErrorSound();
            }
        }

        private void XacNhanMua()
        {
            if (m_ItemDangMua == null || m_EconomySystem == null)
            {
                LogLoi("Không thể mua - thiếu thông tin!");
                return;
            }

            int giaMua = Mathf.RoundToInt(m_ItemDangMua.BasePrice * m_PhanTramTangGiaMua);
            
            // Kiểm tra đủ tiền và trừ tiền
            if (m_EconomySystem.CurrencyManager.TruTien(giaMua))
            {
                // Thêm vật phẩm vào inventory
                if (m_EconomySystem.PlayerInventory.ThemVatPham(m_ItemDangMua.ItemId, 1))
                {
                    // Xóa buy token
                    if (m_ObjectTrongBuyZone != null)
                    {
                        Destroy(m_ObjectTrongBuyZone);
                        m_ObjectTrongBuyZone = null;
                    }

                    // Effects
                    if (m_EffectMua != null)
                        m_EffectMua.Play();

                    if (m_AudioSource != null && m_SoundMua != null)
                        m_AudioSource.PlayOneShot(m_SoundMua);

                    OnItemBought?.Invoke(this, m_ItemDangMua.ItemId, giaMua);
                    Log($"Mua thành công: {m_ItemDangMua.ItemName} - Trả {giaMua} Lea");

                    m_ItemDangMua = null;
                    AnPanelMua();
                }
                else
                {
                    // Hoàn tiền nếu không thể thêm vào inventory
                    m_EconomySystem.CurrencyManager.ThemTien(giaMua);
                    LogLoi("Kho hàng đầy!");
                    PlayErrorSound();
                }
            }
            else
            {
                LogLoi("Không đủ tiền!");
                PlayErrorSound();
            }
        }

        private void HuyBan()
        {
            Log("Hủy bán");
            AnPanelBan();
        }

        private void HuyMua()
        {
            Log("Hủy mua");
            AnPanelMua();
        }

        private void PlayErrorSound()
        {
            if (m_AudioSource != null && m_SoundLoi != null)
                m_AudioSource.PlayOneShot(m_SoundLoi);
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[AdvancedTradingMachine-{m_TenMay}] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[AdvancedTradingMachine-{m_TenMay}] {message}");
        }

        public void ThemItemCoTheMua(string itemId, string itemName, int basePrice)
        {
            var item = new TradableItem(itemId, itemName, basePrice);
            m_DanhSachItemCoTheMua.Add(item);
            Log($"Thêm item có thể mua: {itemName}");
        }

        public void XoaItemCoTheMua(string itemId)
        {
            m_DanhSachItemCoTheMua.RemoveAll(x => x.ItemId == itemId);
            Log($"Xóa item có thể mua: {itemId}");
        }
        #endregion

        #region Gizmos
        private void OnDrawGizmos()
        {
            // Vẽ khoảng cách tương tác
            Gizmos.color = m_PlayerTrongZone ? Color.green : Color.white;
            Gizmos.DrawWireSphere(transform.position, m_KhoangCachTuongTac);

            // Vẽ sell zone
            if (m_SellZone != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(m_SellZone.position, m_SellZone.localScale);
            }

            // Vẽ buy zone
            if (m_BuyZone != null)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawWireCube(m_BuyZone.position, m_BuyZone.localScale);
            }
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Test Hiển Thị Prompt")]
        private void TestHienThiPrompt()
        {
            m_PlayerTrongZone = true;
            HienThiPrompt();
        }

        [ContextMenu("Hiển Thị Thông Tin")]
        private void HienThiThongTin()
        {
            string thongTin = $"=== {m_TenMay} ===\n";
            thongTin += $"Player trong zone: {m_PlayerTrongZone}\n";
            thongTin += $"Object trong sell zone: {(m_ObjectTrongSellZone?.name ?? "Không có")}\n";
            thongTin += $"Object trong buy zone: {(m_ObjectTrongBuyZone?.name ?? "Không có")}\n";
            thongTin += $"Items có thể mua: {m_DanhSachItemCoTheMua.Count}\n";
            Debug.Log(thongTin);
        }
        #endif
        #endregion
    }

    /// <summary>
    /// Component cho vật phẩm có thể giao dịch
    /// </summary>
    [System.Serializable]
    public class TradableItem : MonoBehaviour
    {
        [Header("📦 Item Info")]
        public string ItemId;
        public string ItemName;
        public int BasePrice;
        public Sprite Icon;

        public TradableItem(string itemId, string itemName, int basePrice)
        {
            ItemId = itemId;
            ItemName = itemName;
            BasePrice = basePrice;
        }
    }

    /// <summary>
    /// Component cho token mua hàng
    /// </summary>
    public class BuyToken : MonoBehaviour
    {
        [Header("🛒 Buy Token")]
        public string ItemId;
        public string ItemName;
    }

    /// <summary>
    /// Detector cho trigger zones
    /// </summary>
    public class TriggerZoneDetector : MonoBehaviour
    {
        public event Action<GameObject> OnObjectEnter;
        public event Action<GameObject> OnObjectExit;

        private void OnTriggerEnter(Collider other)
        {
            OnObjectEnter?.Invoke(other.gameObject);
        }

        private void OnTriggerExit(Collider other)
        {
            OnObjectExit?.Invoke(other.gameObject);
        }
    }
}
