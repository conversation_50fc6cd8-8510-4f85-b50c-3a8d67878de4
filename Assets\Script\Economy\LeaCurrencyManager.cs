using UnityEngine;
using System;

namespace EconomySystem
{
    /// <summary>
    /// Qu<PERSON>n lý hệ thống tiền tệ Lea cho game
    /// Hỗ trợ auto-save, events và validation
    /// </summary>
    public class LeaCurrencyManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("💰 Cài Đặt Tiền Tệ Lea")]
        [SerializeField] private int m_SoTienHienTai = 100;
        [SerializeField] private int m_SoTienToiDa = 999999;
        [SerializeField] private bool m_TuDongLuuTien = true;
        [SerializeField] private bool m_HienThiLog = true;

        [Header("💾 Cài Đặt Lưu Trữ")]
        [SerializeField] private string m_KeyLuuTru = "LeaCurrency";
        [SerializeField] private float m_KhoangThoiGianLuu = 30f;

        [Header("🎨 Hiệu Ứng")]
        [SerializeField] private bool m_CoHieuUng = true;
        [SerializeField] private float m_ThoiGianHieuUng = 0.5f;
        #endregion

        #region Events
        public static event Action<int> OnLeaChanged;
        public static event Action<int, int> OnLeaTransaction; // amount, newTotal
        public static event Action<string> OnTransactionFailed;
        #endregion

        #region Properties
        public int SoTienHienTai => m_SoTienHienTai;
        public int SoTienToiDa => m_SoTienToiDa;
        public bool CoTheMua(int gia) => m_SoTienHienTai >= gia;
        public float PhanTramTien => (float)m_SoTienHienTai / m_SoTienToiDa;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TaiDuLieu();
        }

        private void Start()
        {
            if (m_TuDongLuuTien)
            {
                InvokeRepeating(nameof(LuuDuLieu), m_KhoangThoiGianLuu, m_KhoangThoiGianLuu);
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && m_TuDongLuuTien)
                LuuDuLieu();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && m_TuDongLuuTien)
                LuuDuLieu();
        }

        private void OnDestroy()
        {
            if (m_TuDongLuuTien)
                LuuDuLieu();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thêm tiền Lea
        /// </summary>
        public bool ThemTien(int soTien)
        {
            if (soTien <= 0)
            {
                LogLoi($"Số tiền không hợp lệ: {soTien}");
                return false;
            }

            int tienMoi = Mathf.Min(m_SoTienHienTai + soTien, m_SoTienToiDa);
            int tienThemThucTe = tienMoi - m_SoTienHienTai;
            
            m_SoTienHienTai = tienMoi;
            
            Log($"Thêm {tienThemThucTe} Lea. Tổng: {m_SoTienHienTai}");
            
            OnLeaChanged?.Invoke(m_SoTienHienTai);
            OnLeaTransaction?.Invoke(tienThemThucTe, m_SoTienHienTai);
            
            return tienThemThucTe == soTien;
        }

        /// <summary>
        /// Trừ tiền Lea
        /// </summary>
        public bool TruTien(int soTien)
        {
            if (soTien <= 0)
            {
                LogLoi($"Số tiền không hợp lệ: {soTien}");
                return false;
            }

            if (m_SoTienHienTai < soTien)
            {
                string thongBao = $"Không đủ tiền! Cần: {soTien}, Có: {m_SoTienHienTai}";
                LogLoi(thongBao);
                OnTransactionFailed?.Invoke(thongBao);
                return false;
            }

            m_SoTienHienTai -= soTien;
            
            Log($"Trừ {soTien} Lea. Còn lại: {m_SoTienHienTai}");
            
            OnLeaChanged?.Invoke(m_SoTienHienTai);
            OnLeaTransaction?.Invoke(-soTien, m_SoTienHienTai);
            
            return true;
        }

        /// <summary>
        /// Đặt số tiền cụ thể
        /// </summary>
        public void DatTien(int soTien)
        {
            soTien = Mathf.Clamp(soTien, 0, m_SoTienToiDa);
            m_SoTienHienTai = soTien;
            
            Log($"Đặt tiền: {m_SoTienHienTai} Lea");
            OnLeaChanged?.Invoke(m_SoTienHienTai);
        }

        /// <summary>
        /// Reset về số tiền ban đầu
        /// </summary>
        public void ResetTien()
        {
            m_SoTienHienTai = 100; // Số tiền mặc định
            Log("Reset tiền về 100 Lea");
            OnLeaChanged?.Invoke(m_SoTienHienTai);
        }
        #endregion

        #region Data Management
        private void TaiDuLieu()
        {
            if (PlayerPrefs.HasKey(m_KeyLuuTru))
            {
                m_SoTienHienTai = PlayerPrefs.GetInt(m_KeyLuuTru, 100);
                Log($"Tải dữ liệu: {m_SoTienHienTai} Lea");
            }
        }

        private void LuuDuLieu()
        {
            PlayerPrefs.SetInt(m_KeyLuuTru, m_SoTienHienTai);
            PlayerPrefs.Save();
            Log($"Lưu dữ liệu: {m_SoTienHienTai} Lea");
        }

        public void XoaDuLieu()
        {
            PlayerPrefs.DeleteKey(m_KeyLuuTru);
            m_SoTienHienTai = 100;
            Log("Xóa dữ liệu tiền tệ");
            OnLeaChanged?.Invoke(m_SoTienHienTai);
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[LeaCurrencyManager] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[LeaCurrencyManager] {message}");
        }

        public string DinhDangTien(int soTien)
        {
            return $"{soTien:N0} Lea";
        }

        public string DinhDangTienHienTai()
        {
            return DinhDangTien(m_SoTienHienTai);
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Thêm 1000 Lea")]
        private void Them1000Lea()
        {
            ThemTien(1000);
        }

        [ContextMenu("Trừ 500 Lea")]
        private void Tru500Lea()
        {
            TruTien(500);
        }

        [ContextMenu("Reset Tiền")]
        private void ResetTienMenu()
        {
            ResetTien();
        }

        [ContextMenu("Hiển Thị Thông Tin")]
        private void HienThiThongTin()
        {
            Debug.Log($"=== THÔNG TIN TIỀN TỆ LEA ===\n" +
                     $"Số tiền hiện tại: {DinhDangTienHienTai()}\n" +
                     $"Số tiền tối đa: {DinhDangTien(m_SoTienToiDa)}\n" +
                     $"Phần trăm: {PhanTramTien:P1}\n" +
                     $"Tự động lưu: {(m_TuDongLuuTien ? "Bật" : "Tắt")}");
        }
        #endif
        #endregion
    }
}
