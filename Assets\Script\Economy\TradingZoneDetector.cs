using UnityEngine;
using System.Collections.Generic;
using System;

namespace EconomySystem
{
    /// <summary>
    /// Detector cho trigger zones của Trading Machine
    /// Qu<PERSON>n lý việc phát hiện player v<PERSON> hiển thị prompts
    /// </summary>
    public class TradingZoneDetector : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎯 Zone Settings")]
        [SerializeField] private LayerMask m_PlayerLayer = -1;
        [SerializeField] private float m_KhoangCachTuongTac = 3f;
        [SerializeField] private bool m_HienThiGizmos = true;
        [SerializeField] private Color m_MauGizmos = Color.yellow;

        [Header("🔊 Audio")]
        [SerializeField] private AudioSource m_AudioSource;
        [SerializeField] private AudioClip m_SoundVaoZone;
        [SerializeField] private AudioClip m_SoundRoiZone;

        [Header("✨ Visual Effects")]
        [SerializeField] private ParticleSystem m_EffectVaoZone;
        [SerializeField] private ParticleSystem m_EffectRoiZone;
        [SerializeField] private GameObject m_IndicatorZone;
        [SerializeField] private Light m_LightZone;

        [Header("🔧 Settings")]
        [SerializeField] private bool m_HienThiLog = true;
        [SerializeField] private bool m_TuDongTimTradingMachine = true;
        #endregion

        #region Private Fields
        private TradingMachine m_TradingMachine;
        private TradingMachineUI m_TradingMachineUI;
        private List<GameObject> m_PlayersInZone = new List<GameObject>();
        private bool m_ZoneActive = false;
        #endregion

        #region Events
        public static event Action<TradingZoneDetector, GameObject> OnPlayerEnterZone;
        public static event Action<TradingZoneDetector, GameObject> OnPlayerExitZone;
        public static event Action<TradingZoneDetector, bool> OnZoneActiveChanged;
        #endregion

        #region Properties
        public bool CoPlayerTrongZone => m_PlayersInZone.Count > 0;
        public int SoLuongPlayerTrongZone => m_PlayersInZone.Count;
        public TradingMachine TradingMachine => m_TradingMachine;
        public bool ZoneActive => m_ZoneActive;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            ThietLapComponents();
        }

        private void Start()
        {
            if (m_TuDongTimTradingMachine)
                TimTradingMachine();
            
            ThietLapZone();
        }

        private void OnTriggerEnter(Collider other)
        {
            if (KiemTraPlayer(other))
            {
                PlayerVaoZone(other.gameObject);
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (KiemTraPlayer(other))
            {
                PlayerRoiZone(other.gameObject);
            }
        }

        private void OnDrawGizmos()
        {
            if (m_HienThiGizmos)
            {
                VeGizmos();
            }
        }
        #endregion

        #region Setup Methods
        private void ThietLapComponents()
        {
            // Đảm bảo có Collider và là trigger
            Collider col = GetComponent<Collider>();
            if (col == null)
            {
                col = gameObject.AddComponent<BoxCollider>();
                Log("Đã thêm BoxCollider tự động");
            }
            col.isTrigger = true;

            // Thiết lập AudioSource
            if (m_AudioSource == null)
                m_AudioSource = GetComponent<AudioSource>();

            if (m_AudioSource == null)
            {
                m_AudioSource = gameObject.AddComponent<AudioSource>();
                m_AudioSource.playOnAwake = false;
                m_AudioSource.spatialBlend = 1f; // 3D sound
            }
        }

        private void TimTradingMachine()
        {
            // Tìm trong parent hoặc cùng GameObject
            m_TradingMachine = GetComponentInParent<TradingMachine>();
            if (m_TradingMachine == null)
                m_TradingMachine = GetComponent<TradingMachine>();

            // Tìm UI
            m_TradingMachineUI = GetComponentInParent<TradingMachineUI>();
            if (m_TradingMachineUI == null)
                m_TradingMachineUI = GetComponent<TradingMachineUI>();

            if (m_TradingMachine != null)
                Log($"Đã tìm thấy TradingMachine: {m_TradingMachine.name}");
            else
                LogLoi("Không tìm thấy TradingMachine!");
        }

        private void ThietLapZone()
        {
            DatTrangThaiZone(true);
            
            // Thiết lập indicator
            if (m_IndicatorZone != null)
                m_IndicatorZone.SetActive(false);

            // Thiết lập light
            if (m_LightZone != null)
            {
                m_LightZone.enabled = false;
                m_LightZone.color = m_MauGizmos;
            }

            Log("Thiết lập zone hoàn tất");
        }
        #endregion

        #region Player Detection
        private bool KiemTraPlayer(Collider other)
        {
            return ((1 << other.gameObject.layer) & m_PlayerLayer) != 0;
        }

        private void PlayerVaoZone(GameObject player)
        {
            if (m_PlayersInZone.Contains(player))
                return;

            m_PlayersInZone.Add(player);
            
            Log($"Player {player.name} vào zone");
            
            // Effects
            if (m_EffectVaoZone != null)
                m_EffectVaoZone.Play();

            if (m_AudioSource != null && m_SoundVaoZone != null)
                m_AudioSource.PlayOneShot(m_SoundVaoZone);

            // Hiển thị indicator
            if (m_IndicatorZone != null)
                m_IndicatorZone.SetActive(true);

            if (m_LightZone != null)
                m_LightZone.enabled = true;

            // Hiển thị prompt UI
            if (m_TradingMachineUI != null)
                m_TradingMachineUI.HienThiPrompt(true);

            // Events
            OnPlayerEnterZone?.Invoke(this, player);
            
            // Cập nhật trạng thái zone
            if (m_PlayersInZone.Count == 1)
            {
                DatTrangThaiZone(true);
            }
        }

        private void PlayerRoiZone(GameObject player)
        {
            if (!m_PlayersInZone.Contains(player))
                return;

            m_PlayersInZone.Remove(player);
            
            Log($"Player {player.name} rời zone");
            
            // Effects
            if (m_EffectRoiZone != null)
                m_EffectRoiZone.Play();

            if (m_AudioSource != null && m_SoundRoiZone != null)
                m_AudioSource.PlayOneShot(m_SoundRoiZone);

            // Ẩn indicator nếu không còn player nào
            if (m_PlayersInZone.Count == 0)
            {
                if (m_IndicatorZone != null)
                    m_IndicatorZone.SetActive(false);

                if (m_LightZone != null)
                    m_LightZone.enabled = false;

                // Ẩn prompt UI
                if (m_TradingMachineUI != null)
                    m_TradingMachineUI.HienThiPrompt(false);

                DatTrangThaiZone(false);
            }

            // Events
            OnPlayerExitZone?.Invoke(this, player);
        }

        private void DatTrangThaiZone(bool active)
        {
            if (m_ZoneActive != active)
            {
                m_ZoneActive = active;
                OnZoneActiveChanged?.Invoke(this, active);
                Log($"Zone active: {active}");
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Kiểm tra player có trong zone không
        /// </summary>
        public bool KiemTraPlayerTrongZone(GameObject player)
        {
            return m_PlayersInZone.Contains(player);
        }

        /// <summary>
        /// Lấy danh sách players trong zone
        /// </summary>
        public List<GameObject> LayDanhSachPlayers()
        {
            return new List<GameObject>(m_PlayersInZone);
        }

        /// <summary>
        /// Xóa tất cả players khỏi zone (force)
        /// </summary>
        public void XoaTatCaPlayers()
        {
            var playersToRemove = new List<GameObject>(m_PlayersInZone);
            foreach (var player in playersToRemove)
            {
                PlayerRoiZone(player);
            }
            
            m_PlayersInZone.Clear();
            Log("Đã xóa tất cả players khỏi zone");
        }

        /// <summary>
        /// Đặt TradingMachine reference
        /// </summary>
        public void DatTradingMachine(TradingMachine tradingMachine)
        {
            m_TradingMachine = tradingMachine;
            Log($"Đã đặt TradingMachine: {tradingMachine?.name}");
        }

        /// <summary>
        /// Đặt TradingMachineUI reference
        /// </summary>
        public void DatTradingMachineUI(TradingMachineUI tradingMachineUI)
        {
            m_TradingMachineUI = tradingMachineUI;
            Log($"Đã đặt TradingMachineUI: {tradingMachineUI?.name}");
        }

        /// <summary>
        /// Bật/tắt zone detection
        /// </summary>
        public void BatTatZone(bool enable)
        {
            Collider col = GetComponent<Collider>();
            if (col != null)
                col.enabled = enable;

            if (!enable)
                XoaTatCaPlayers();

            Log($"Zone detection: {(enable ? "Bật" : "Tắt")}");
        }

        /// <summary>
        /// Đặt khoảng cách tương tác
        /// </summary>
        public void DatKhoangCachTuongTac(float khoangCach)
        {
            m_KhoangCachTuongTac = Mathf.Max(0.1f, khoangCach);
            
            // Cập nhật collider size nếu là BoxCollider
            BoxCollider boxCol = GetComponent<BoxCollider>();
            if (boxCol != null)
            {
                boxCol.size = Vector3.one * m_KhoangCachTuongTac * 2f;
            }
            
            Log($"Đặt khoảng cách tương tác: {m_KhoangCachTuongTac}");
        }
        #endregion

        #region Visual Methods
        private void VeGizmos()
        {
            Gizmos.color = m_ZoneActive ? m_MauGizmos : Color.gray;
            
            // Vẽ wireframe của trigger zone
            Collider col = GetComponent<Collider>();
            if (col is BoxCollider boxCol)
            {
                Matrix4x4 oldMatrix = Gizmos.matrix;
                Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, transform.lossyScale);
                Gizmos.DrawWireCube(boxCol.center, boxCol.size);
                Gizmos.matrix = oldMatrix;
            }
            else if (col is SphereCollider sphereCol)
            {
                Gizmos.DrawWireSphere(transform.position + sphereCol.center, sphereCol.radius * transform.lossyScale.x);
            }
            
            // Vẽ khoảng cách tương tác
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, m_KhoangCachTuongTac);
            
            // Vẽ players trong zone
            Gizmos.color = Color.green;
            foreach (var player in m_PlayersInZone)
            {
                if (player != null)
                {
                    Gizmos.DrawLine(transform.position, player.transform.position);
                    Gizmos.DrawWireSphere(player.transform.position, 0.5f);
                }
            }
        }

        /// <summary>
        /// Cập nhật màu indicator dựa trên trạng thái
        /// </summary>
        public void CapNhatMauIndicator(Color mau)
        {
            m_MauGizmos = mau;
            
            if (m_LightZone != null)
                m_LightZone.color = mau;

            // Cập nhật material của indicator nếu có
            if (m_IndicatorZone != null)
            {
                Renderer renderer = m_IndicatorZone.GetComponent<Renderer>();
                if (renderer != null && renderer.material != null)
                {
                    renderer.material.color = mau;
                }
            }
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[TradingZoneDetector] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[TradingZoneDetector] {message}");
        }

        /// <summary>
        /// Lấy thông tin chi tiết về zone
        /// </summary>
        public string LayThongTinZone()
        {
            string thongTin = $"=== TRADING ZONE INFO ===\n";
            thongTin += $"Zone Active: {m_ZoneActive}\n";
            thongTin += $"Players trong zone: {m_PlayersInZone.Count}\n";
            thongTin += $"Khoảng cách tương tác: {m_KhoangCachTuongTac}\n";
            thongTin += $"TradingMachine: {(m_TradingMachine != null ? m_TradingMachine.name : "Null")}\n";
            
            if (m_PlayersInZone.Count > 0)
            {
                thongTin += "Danh sách players:\n";
                foreach (var player in m_PlayersInZone)
                {
                    thongTin += $"- {player.name}\n";
                }
            }
            
            return thongTin;
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Test Player Vào Zone")]
        private void TestPlayerVaoZone()
        {
            GameObject testPlayer = GameObject.FindWithTag("Player");
            if (testPlayer != null)
                PlayerVaoZone(testPlayer);
            else
                LogLoi("Không tìm thấy Player để test!");
        }

        [ContextMenu("Test Player Rời Zone")]
        private void TestPlayerRoiZone()
        {
            if (m_PlayersInZone.Count > 0)
                PlayerRoiZone(m_PlayersInZone[0]);
            else
                Log("Không có player nào trong zone để test!");
        }

        [ContextMenu("Hiển Thị Thông Tin Zone")]
        private void HienThiThongTinZone()
        {
            Debug.Log(LayThongTinZone());
        }

        [ContextMenu("Xóa Tất Cả Players")]
        private void XoaTatCaPlayersMenu()
        {
            XoaTatCaPlayers();
        }
        #endif
        #endregion
    }
}
