using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace EconomySystem
{
    /// <summary>
    /// Quản lý giao diện kho hàng người chơi
    /// Hi<PERSON><PERSON> thị danh sách vật phẩm, số lượng và thông tin chi tiết
    /// </summary>
    public class InventoryUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 UI References")]
        [SerializeField] private GameObject m_PanelKhoHang;
        [SerializeField] private Transform m_ContainerVatPham;
        [SerializeField] private GameObject m_PrefabSlotVatPham;
        [SerializeField] private ScrollRect m_ScrollRect;

        [Header("📊 Thông Tin Kho")]
        [SerializeField] private TextMeshProUGUI m_TextSoSlot;
        [SerializeField] private Slider m_SliderDayKho;
        [SerializeField] private TextMeshProUGUI m_TextPhanTramDay;

        [Header("🔍 Tìm Kiếm")]
        [SerializeField] private TMP_InputField m_InputTimKiem;
        [SerializeField] private But<PERSON> m_ButtonTimKiem;
        [SerializeField] private Button m_ButtonXoaTimKiem;

        [Header("🔧 Điều Khiển")]
        [SerializeField] private Button m_ButtonDongKho;
        [SerializeField] private Button m_ButtonSapXep;
        [SerializeField] private Button m_ButtonXoaToanBo;

        [Header("⚙️ Settings")]
        [SerializeField] private bool m_TuDongCapNhat = true;
        [SerializeField] private bool m_HienThiLog = false;
        [SerializeField] private KeyCode m_PhimMoKho = KeyCode.Tab;
        #endregion

        #region Private Fields
        private PlayerInventory m_PlayerInventory;
        private List<InventorySlotUI> m_DanhSachSlotUI = new List<InventorySlotUI>();
        private string m_TuKhoaTimKiem = "";
        private bool m_KhoMo = false;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TimPlayerInventory();
            ThietLapUI();
        }

        private void Start()
        {
            DangKyEvents();
            CapNhatUI();
        }

        private void Update()
        {
            if (Input.GetKeyDown(m_PhimMoKho))
            {
                ChuyenDoiTrangThaiKho();
            }
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
        }
        #endregion

        #region Event Management
        private void DangKyEvents()
        {
            PlayerInventory.OnInventoryChanged += OnInventoryChanged;
            PlayerInventory.OnItemAdded += OnItemAdded;
            PlayerInventory.OnItemRemoved += OnItemRemoved;
            PlayerInventory.OnInventoryFull += OnInventoryFull;

            // UI Events
            if (m_ButtonDongKho != null)
                m_ButtonDongKho.onClick.AddListener(DongKho);

            if (m_ButtonSapXep != null)
                m_ButtonSapXep.onClick.AddListener(SapXepKho);

            if (m_ButtonXoaToanBo != null)
                m_ButtonXoaToanBo.onClick.AddListener(XoaToanBoKho);

            if (m_ButtonTimKiem != null)
                m_ButtonTimKiem.onClick.AddListener(TimKiem);

            if (m_ButtonXoaTimKiem != null)
                m_ButtonXoaTimKiem.onClick.AddListener(XoaTimKiem);

            if (m_InputTimKiem != null)
                m_InputTimKiem.onValueChanged.AddListener(OnTimKiemChanged);
        }

        private void HuyDangKyEvents()
        {
            PlayerInventory.OnInventoryChanged -= OnInventoryChanged;
            PlayerInventory.OnItemAdded -= OnItemAdded;
            PlayerInventory.OnItemRemoved -= OnItemRemoved;
            PlayerInventory.OnInventoryFull -= OnInventoryFull;

            // UI Events
            if (m_ButtonDongKho != null)
                m_ButtonDongKho.onClick.RemoveAllListeners();

            if (m_ButtonSapXep != null)
                m_ButtonSapXep.onClick.RemoveAllListeners();

            if (m_ButtonXoaToanBo != null)
                m_ButtonXoaToanBo.onClick.RemoveAllListeners();

            if (m_ButtonTimKiem != null)
                m_ButtonTimKiem.onClick.RemoveAllListeners();

            if (m_ButtonXoaTimKiem != null)
                m_ButtonXoaTimKiem.onClick.RemoveAllListeners();

            if (m_InputTimKiem != null)
                m_InputTimKiem.onValueChanged.RemoveAllListeners();
        }
        #endregion

        #region Event Handlers
        private void OnInventoryChanged()
        {
            if (m_TuDongCapNhat)
                CapNhatUI();
        }

        private void OnItemAdded(InventoryItem item)
        {
            Log($"Đã thêm vật phẩm: {item.ItemId} x{item.SoLuong}");
        }

        private void OnItemRemoved(InventoryItem item)
        {
            Log($"Đã xóa vật phẩm: {item.ItemId}");
        }

        private void OnInventoryFull(string message)
        {
            Log($"Kho đầy: {message}");
            // Có thể hiển thị popup thông báo ở đây
        }

        private void OnTimKiemChanged(string tuKhoa)
        {
            m_TuKhoaTimKiem = tuKhoa;
            if (string.IsNullOrEmpty(tuKhoa))
                CapNhatDanhSachVatPham();
        }
        #endregion

        #region UI Management
        private void ThietLapUI()
        {
            if (m_PanelKhoHang != null)
                m_PanelKhoHang.SetActive(false);

            // Thiết lập input field
            if (m_InputTimKiem != null)
            {
                m_InputTimKiem.placeholder.GetComponent<TextMeshProUGUI>().text = "Tìm kiếm vật phẩm...";
            }
        }

        public void MoKho()
        {
            m_KhoMo = true;
            if (m_PanelKhoHang != null)
                m_PanelKhoHang.SetActive(true);
            
            CapNhatUI();
            Log("Mở kho hàng");
        }

        public void DongKho()
        {
            m_KhoMo = false;
            if (m_PanelKhoHang != null)
                m_PanelKhoHang.SetActive(false);
            
            Log("Đóng kho hàng");
        }

        public void ChuyenDoiTrangThaiKho()
        {
            if (m_KhoMo)
                DongKho();
            else
                MoKho();
        }

        private void CapNhatUI()
        {
            if (m_PlayerInventory == null)
                TimPlayerInventory();

            if (m_PlayerInventory != null)
            {
                CapNhatThongTinKho();
                CapNhatDanhSachVatPham();
            }
        }

        private void CapNhatThongTinKho()
        {
            // Cập nhật số slot
            if (m_TextSoSlot != null)
                m_TextSoSlot.text = $"{m_PlayerInventory.SoSlotDaDung}/{m_PlayerInventory.SoSlotConLai + m_PlayerInventory.SoSlotDaDung}";

            // Cập nhật slider
            if (m_SliderDayKho != null)
                m_SliderDayKho.value = m_PlayerInventory.PhanTramDay;

            // Cập nhật phần trăm
            if (m_TextPhanTramDay != null)
                m_TextPhanTramDay.text = $"{m_PlayerInventory.PhanTramDay:P0}";
        }

        private void CapNhatDanhSachVatPham()
        {
            // Xóa các slot cũ
            foreach (var slot in m_DanhSachSlotUI)
            {
                if (slot != null && slot.gameObject != null)
                    DestroyImmediate(slot.gameObject);
            }
            m_DanhSachSlotUI.Clear();

            // Lấy danh sách vật phẩm
            var danhSachVatPham = m_PlayerInventory.DanhSachVatPham;

            // Lọc theo từ khóa tìm kiếm
            if (!string.IsNullOrEmpty(m_TuKhoaTimKiem))
            {
                danhSachVatPham = m_PlayerInventory.TimKiemVatPham(m_TuKhoaTimKiem);
            }

            // Tạo slot UI cho mỗi vật phẩm
            foreach (var vatPham in danhSachVatPham)
            {
                TaoSlotVatPham(vatPham);
            }

            Log($"Cập nhật UI: {danhSachVatPham.Count} vật phẩm");
        }

        private void TaoSlotVatPham(InventoryItem vatPham)
        {
            if (m_PrefabSlotVatPham == null || m_ContainerVatPham == null)
                return;

            GameObject slotObj = Instantiate(m_PrefabSlotVatPham, m_ContainerVatPham);
            InventorySlotUI slotUI = slotObj.GetComponent<InventorySlotUI>();

            if (slotUI != null)
            {
                slotUI.ThietLapSlot(vatPham);
                m_DanhSachSlotUI.Add(slotUI);
            }
        }
        #endregion

        #region Button Handlers
        private void SapXepKho()
        {
            if (m_PlayerInventory != null)
            {
                m_PlayerInventory.SapXepKho();
                Log("Đã sắp xếp kho hàng");
            }
        }

        private void XoaToanBoKho()
        {
            if (m_PlayerInventory != null)
            {
                m_PlayerInventory.XoaToanBoKho();
                Log("Đã xóa toàn bộ kho hàng");
            }
        }

        private void TimKiem()
        {
            if (m_InputTimKiem != null)
            {
                m_TuKhoaTimKiem = m_InputTimKiem.text;
                CapNhatDanhSachVatPham();
                Log($"Tìm kiếm: {m_TuKhoaTimKiem}");
            }
        }

        private void XoaTimKiem()
        {
            if (m_InputTimKiem != null)
            {
                m_InputTimKiem.text = "";
                m_TuKhoaTimKiem = "";
                CapNhatDanhSachVatPham();
                Log("Xóa tìm kiếm");
            }
        }
        #endregion

        #region Utility Methods
        private void TimPlayerInventory()
        {
            if (m_PlayerInventory == null)
                m_PlayerInventory = FindObjectOfType<PlayerInventory>();
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[InventoryUI] {message}");
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Mở Kho Hàng")]
        private void MoKhoMenu()
        {
            MoKho();
        }

        [ContextMenu("Đóng Kho Hàng")]
        private void DongKhoMenu()
        {
            DongKho();
        }

        [ContextMenu("Cập Nhật UI")]
        private void CapNhatUIMenu()
        {
            CapNhatUI();
        }
        #endif
        #endregion
    }

    /// <summary>
    /// Component cho từng slot vật phẩm trong UI
    /// </summary>
    public class InventorySlotUI : MonoBehaviour
    {
        [Header("UI References")]
        public Image iconVatPham;
        public TextMeshProUGUI textTenVatPham;
        public TextMeshProUGUI textSoLuong;
        public Button buttonSlot;

        private InventoryItem m_VatPham;

        public void ThietLapSlot(InventoryItem vatPham)
        {
            m_VatPham = vatPham;

            if (textTenVatPham != null)
                textTenVatPham.text = vatPham.ItemId;

            if (textSoLuong != null)
                textSoLuong.text = vatPham.SoLuong.ToString();

            // Có thể thêm logic load icon từ ItemDatabase ở đây

            if (buttonSlot != null)
                buttonSlot.onClick.AddListener(OnSlotClicked);
        }

        private void OnSlotClicked()
        {
            Debug.Log($"Clicked on {m_VatPham.ItemId}");
            // Có thể mở popup chi tiết vật phẩm ở đây
        }
    }
}
