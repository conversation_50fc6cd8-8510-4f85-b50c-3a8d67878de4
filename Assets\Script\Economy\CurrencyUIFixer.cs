using UnityEngine;
using TMPro;

namespace EconomySystem
{
    /// <summary>
    /// Script để sửa lỗi Currency UI không cập nhật
    /// Đảm bảo UI luôn sync với CurrencyManager
    /// </summary>
    public class CurrencyUIFixer : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🔧 UI References")]
        [SerializeField] private TextMeshProUGUI m_TextTienTe;
        [SerializeField] private bool m_TuDongTimText = true;
        [SerializeField] private bool m_HienThiLog = true;
        [SerializeField] private string m_DinhDangTien = "{0:N0} Lea";

        [Header("⚙️ Settings")]
        [SerializeField] private float m_TanSoCapNhat = 0.1f; // Cập nhật mỗi 0.1 giây
        [SerializeField] private bool m_CapNhatLienTuc = true;
        [SerializeField] private bool m_SuDungEvents = true;
        #endregion

        #region Private Fields
        private LeaCurrencyManager m_CurrencyManager;
        private int m_TienTruoc = -1;
        private float m_ThoiGianCapNhatCuoi = 0f;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (m_TuDongTimText && m_TextTienTe == null)
            {
                TimTextComponent();
            }
        }

        private void Start()
        {
            TimCurrencyManager();
            
            if (m_SuDungEvents)
            {
                DangKyEvents();
            }
            
            // Cập nhật ngay lập tức
            CapNhatUI();
        }

        private void Update()
        {
            if (m_CapNhatLienTuc)
            {
                // Cập nhật theo tần số
                if (Time.time - m_ThoiGianCapNhatCuoi >= m_TanSoCapNhat)
                {
                    CapNhatUI();
                    m_ThoiGianCapNhatCuoi = Time.time;
                }
            }
        }

        private void OnDestroy()
        {
            if (m_SuDungEvents)
            {
                HuyDangKyEvents();
            }
        }
        #endregion

        #region Setup Methods
        private void TimTextComponent()
        {
            // Tìm trong chính GameObject này
            m_TextTienTe = GetComponent<TextMeshProUGUI>();
            
            if (m_TextTienTe == null)
            {
                // Tìm trong children
                m_TextTienTe = GetComponentInChildren<TextMeshProUGUI>();
            }
            
            if (m_TextTienTe == null)
            {
                // Tìm theo tên
                var textObjects = FindObjectsOfType<TextMeshProUGUI>();
                foreach (var textObj in textObjects)
                {
                    if (textObj.name.ToLower().Contains("currency") || 
                        textObj.name.ToLower().Contains("money") ||
                        textObj.name.ToLower().Contains("lea") ||
                        textObj.name.ToLower().Contains("tien"))
                    {
                        m_TextTienTe = textObj;
                        break;
                    }
                }
            }
            
            if (m_TextTienTe != null)
            {
                Log($"Tìm thấy Text component: {m_TextTienTe.name}");
            }
            else
            {
                LogLoi("Không tìm thấy TextMeshProUGUI component!");
            }
        }

        private void TimCurrencyManager()
        {
            m_CurrencyManager = FindObjectOfType<LeaCurrencyManager>();
            
            if (m_CurrencyManager == null)
            {
                LogLoi("Không tìm thấy LeaCurrencyManager!");
            }
            else
            {
                Log($"Tìm thấy CurrencyManager: {m_CurrencyManager.name}");
            }
        }

        private void DangKyEvents()
        {
            LeaCurrencyManager.OnLeaChanged += OnLeaChanged;
            LeaCurrencyManager.OnLeaTransaction += OnLeaTransaction;
            Log("Đã đăng ký events");
        }

        private void HuyDangKyEvents()
        {
            LeaCurrencyManager.OnLeaChanged -= OnLeaChanged;
            LeaCurrencyManager.OnLeaTransaction -= OnLeaTransaction;
            Log("Đã hủy đăng ký events");
        }
        #endregion

        #region Event Handlers
        private void OnLeaChanged(int tienMoi)
        {
            Log($"Event: Tiền thay đổi thành {tienMoi}");
            CapNhatUI();
        }

        private void OnLeaTransaction(int soTienThayDoi, int tongTienMoi)
        {
            Log($"Event: Giao dịch {soTienThayDoi:+#;-#;0}, Tổng: {tongTienMoi}");
            CapNhatUI();
        }
        #endregion

        #region UI Update Methods
        private void CapNhatUI()
        {
            if (m_CurrencyManager == null)
            {
                TimCurrencyManager();
                if (m_CurrencyManager == null) return;
            }

            if (m_TextTienTe == null)
            {
                if (m_TuDongTimText)
                {
                    TimTextComponent();
                }
                if (m_TextTienTe == null) return;
            }

            int tienHienTai = m_CurrencyManager.SoTienHienTai;
            
            // Chỉ cập nhật nếu có thay đổi
            if (tienHienTai != m_TienTruoc)
            {
                string textMoi = string.Format(m_DinhDangTien, tienHienTai);
                m_TextTienTe.text = textMoi;
                
                Log($"Cập nhật UI: {m_TienTruoc} → {tienHienTai}");
                m_TienTruoc = tienHienTai;
            }
        }

        /// <summary>
        /// Buộc cập nhật UI ngay lập tức
        /// </summary>
        public void BuocCapNhatUI()
        {
            m_TienTruoc = -1; // Reset để buộc cập nhật
            CapNhatUI();
            Log("Buộc cập nhật UI");
        }

        /// <summary>
        /// Đặt text component mới
        /// </summary>
        public void DatTextComponent(TextMeshProUGUI textComponent)
        {
            m_TextTienTe = textComponent;
            Log($"Đặt text component: {textComponent?.name}");
            BuocCapNhatUI();
        }

        /// <summary>
        /// Đặt định dạng tiền mới
        /// </summary>
        public void DatDinhDangTien(string dinhDangMoi)
        {
            m_DinhDangTien = dinhDangMoi;
            Log($"Đặt định dạng tiền: {dinhDangMoi}");
            BuocCapNhatUI();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Kiểm tra trạng thái hệ thống
        /// </summary>
        public bool KiemTraTrangThai()
        {
            bool hopLe = true;
            string baoCao = "=== KIỂM TRA CURRENCY UI ===\n";
            
            if (m_CurrencyManager == null)
            {
                baoCao += "❌ CurrencyManager: Không tìm thấy\n";
                hopLe = false;
            }
            else
            {
                baoCao += $"✅ CurrencyManager: {m_CurrencyManager.name}\n";
                baoCao += $"✅ Tiền hiện tại: {m_CurrencyManager.SoTienHienTai}\n";
            }
            
            if (m_TextTienTe == null)
            {
                baoCao += "❌ TextMeshProUGUI: Không tìm thấy\n";
                hopLe = false;
            }
            else
            {
                baoCao += $"✅ TextMeshProUGUI: {m_TextTienTe.name}\n";
                baoCao += $"✅ Text hiện tại: {m_TextTienTe.text}\n";
            }
            
            baoCao += $"⚙️ Cập nhật liên tục: {m_CapNhatLienTuc}\n";
            baoCao += $"⚙️ Sử dụng events: {m_SuDungEvents}\n";
            baoCao += $"⚙️ Tần số cập nhật: {m_TanSoCapNhat}s\n";
            
            Debug.Log(baoCao);
            return hopLe;
        }

        /// <summary>
        /// Sửa lỗi tự động
        /// </summary>
        public void SuaLoiTuDong()
        {
            Log("Bắt đầu sửa lỗi tự động...");
            
            // Tìm lại components
            TimCurrencyManager();
            TimTextComponent();
            
            // Đăng ký lại events
            if (m_SuDungEvents)
            {
                HuyDangKyEvents();
                DangKyEvents();
            }
            
            // Cập nhật UI
            BuocCapNhatUI();
            
            // Kiểm tra kết quả
            bool thanhCong = KiemTraTrangThai();
            
            if (thanhCong)
            {
                Log("✅ Sửa lỗi thành công!");
            }
            else
            {
                LogLoi("❌ Vẫn còn lỗi sau khi sửa!");
            }
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[CurrencyUIFixer] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[CurrencyUIFixer] {message}");
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Kiểm Tra Trạng Thái")]
        private void KiemTraTrangThaiMenu()
        {
            KiemTraTrangThai();
        }

        [ContextMenu("Sửa Lỗi Tự Động")]
        private void SuaLoiTuDongMenu()
        {
            SuaLoiTuDong();
        }

        [ContextMenu("Buộc Cập Nhật UI")]
        private void BuocCapNhatUIMenu()
        {
            BuocCapNhatUI();
        }

        [ContextMenu("Tìm Text Component")]
        private void TimTextComponentMenu()
        {
            TimTextComponent();
        }

        [ContextMenu("Tìm Currency Manager")]
        private void TimCurrencyManagerMenu()
        {
            TimCurrencyManager();
        }
        #endif
        #endregion
    }
}
