using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

namespace EconomySystem
{
    /// <summary>
    /// UI Controller cho Trading Machine
    /// Quản lý animations và visual feedback
    /// </summary>
    public class TradingMachineUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 Main UI References")]
        [SerializeField] private Canvas m_MainCanvas;
        [SerializeField] private CanvasGroup m_MainCanvasGroup;
        [SerializeField] private GameObject m_PanelGiaoDich;
        [SerializeField] private GameObject m_PanelChinhGia;
        [SerializeField] private GameObject m_PanelPrompt;

        [Header("📝 Text Elements")]
        [SerializeField] private TextMeshProUGUI m_TextTenMay;
        [SerializeField] private TextMeshProUGUI m_TextItemName;
        [SerializeField] private TextMeshProUGUI m_TextSoLuong;
        [SerializeField] private TextMeshProUGUI m_TextGiaMua;
        [SerializeField] private TextMeshProUG<PERSON> m_TextGiaBan;
        [SerializeField] private TextMeshProUG<PERSON> m_TextTongTien;
        [SerializeField] private TextMeshP<PERSON>UG<PERSON> m_TextPrompt;
        [SerializeField] private TextMeshProUGUI m_TextTienHienTai;

        [Header("🔘 Buttons")]
        [SerializeField] private Button m_ButtonMua;
        [SerializeField] private Button m_ButtonBan;
        [SerializeField] private Button m_ButtonDong;
        [SerializeField] private Button m_ButtonChinhGia;
        [SerializeField] private Button m_ButtonLuuGia;
        [SerializeField] private Button m_ButtonHuyChinhGia;

        [Header("🎚️ Sliders & Inputs")]
        [SerializeField] private Slider m_SliderSoLuong;
        [SerializeField] private Slider m_SliderGiaMua;
        [SerializeField] private Slider m_SliderGiaBan;
        [SerializeField] private TMP_InputField m_InputGiaMua;
        [SerializeField] private TMP_InputField m_InputGiaBan;

        [Header("🎭 Animation Settings")]
        [SerializeField] private float m_ThoiGianFadeIn = 0.3f;
        [SerializeField] private float m_ThoiGianFadeOut = 0.2f;
        [SerializeField] private AnimationCurve m_CurveFadeIn = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private AnimationCurve m_CurveFadeOut = AnimationCurve.EaseInOut(0, 1, 1, 0);

        [Header("🌈 Color Settings")]
        [SerializeField] private Color m_MauButtonBinhThuong = Color.white;
        [SerializeField] private Color m_MauButtonMua = Color.green;
        [SerializeField] private Color m_MauButtonBan = Color.yellow;
        [SerializeField] private Color m_MauButtonKhongHoatDong = Color.gray;
        [SerializeField] private Color m_MauTextBinhThuong = Color.white;
        [SerializeField] private Color m_MauTextDuTien = Color.green;
        [SerializeField] private Color m_MauTextKhongDuTien = Color.red;

        [Header("✨ Effects")]
        [SerializeField] private bool m_CoHieuUngScale = true;
        [SerializeField] private float m_ScaleHieuUng = 1.1f;
        [SerializeField] private float m_ThoiGianScale = 0.2f;
        [SerializeField] private ParticleSystem m_EffectMoUI;
        [SerializeField] private ParticleSystem m_EffectDongUI;

        [Header("🔧 Settings")]
        [SerializeField] private bool m_HienThiLog = false;
        [SerializeField] private KeyCode m_PhimMoUI = KeyCode.F;
        [SerializeField] private KeyCode m_PhimDongUI = KeyCode.Escape;
        #endregion

        #region Private Fields
        private TradingMachine m_TradingMachine;
        private EconomySystemManager m_EconomySystem;
        private Coroutine m_CoroutineAnimation;
        private bool m_UIVisible = false;
        private Vector3 m_ScaleGoc = Vector3.one;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            m_TradingMachine = GetComponent<TradingMachine>();
            m_ScaleGoc = transform.localScale;
            ThietLapUI();
        }

        private void Start()
        {
            TimEconomySystem();
            DangKyEvents();
            AnUI();
        }

        private void Update()
        {
            CapNhatTienHienTai();
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
        }
        #endregion

        #region Setup Methods
        private void ThietLapUI()
        {
            // Thiết lập Canvas
            if (m_MainCanvas != null)
            {
                m_MainCanvas.worldCamera = Camera.main;
                m_MainCanvas.sortingOrder = 100;
            }

            // Thiết lập CanvasGroup
            if (m_MainCanvasGroup == null && m_MainCanvas != null)
            {
                m_MainCanvasGroup = m_MainCanvas.gameObject.AddComponent<CanvasGroup>();
            }

            // Thiết lập prompt text
            if (m_TextPrompt != null)
            {
                m_TextPrompt.text = $"Nhấn [{m_PhimMoUI}] để tương tác";
            }

            Log("Thiết lập UI hoàn tất");
        }

        private void TimEconomySystem()
        {
            m_EconomySystem = FindObjectOfType<EconomySystemManager>();
            if (m_EconomySystem == null)
            {
                LogLoi("Không tìm thấy EconomySystemManager!");
            }
        }

        private void DangKyEvents()
        {
            // Economy events
            if (m_EconomySystem != null)
            {
                LeaCurrencyManager.OnLeaChanged += OnTienChanged;
                PlayerInventory.OnInventoryChanged += OnInventoryChanged;
            }

            // Trading machine events
            TradingMachine.OnItemTraded += OnItemTraded;
            TradingMachine.OnPriceChanged += OnPriceChanged;
        }

        private void HuyDangKyEvents()
        {
            LeaCurrencyManager.OnLeaChanged -= OnTienChanged;
            PlayerInventory.OnInventoryChanged -= OnInventoryChanged;
            TradingMachine.OnItemTraded -= OnItemTraded;
            TradingMachine.OnPriceChanged -= OnPriceChanged;
        }
        #endregion

        #region UI Display Methods
        public void HienThiUI()
        {
            if (m_UIVisible) return;

            m_UIVisible = true;
            
            if (m_MainCanvas != null)
                m_MainCanvas.gameObject.SetActive(true);

            // Animation fade in
            if (m_CoroutineAnimation != null)
                StopCoroutine(m_CoroutineAnimation);
            
            m_CoroutineAnimation = StartCoroutine(AnimationFadeIn());

            // Effects
            if (m_EffectMoUI != null)
                m_EffectMoUI.Play();

            Log("Hiển thị UI");
        }

        public void AnUI()
        {
            if (!m_UIVisible) return;

            // Animation fade out
            if (m_CoroutineAnimation != null)
                StopCoroutine(m_CoroutineAnimation);
            
            m_CoroutineAnimation = StartCoroutine(AnimationFadeOut());

            // Effects
            if (m_EffectDongUI != null)
                m_EffectDongUI.Play();

            Log("Ẩn UI");
        }

        public void HienThiPrompt(bool hienThi)
        {
            if (m_PanelPrompt != null)
                m_PanelPrompt.SetActive(hienThi);
        }

        public void ChuyenDoiPanel(bool chinhGiaMode)
        {
            if (m_PanelGiaoDich != null)
                m_PanelGiaoDich.SetActive(!chinhGiaMode);

            if (m_PanelChinhGia != null)
                m_PanelChinhGia.SetActive(chinhGiaMode);

            Log($"Chuyển đổi panel: {(chinhGiaMode ? "Chỉnh giá" : "Giao dịch")}");
        }
        #endregion

        #region UI Update Methods
        public void CapNhatThongTinMay(string tenMay, string itemId)
        {
            if (m_TextTenMay != null)
                m_TextTenMay.text = tenMay;

            if (m_TextItemName != null)
                m_TextItemName.text = itemId;
        }

        public void CapNhatGia(int giaMua, int giaBan)
        {
            if (m_TextGiaMua != null)
                m_TextGiaMua.text = $"Giá mua: {giaMua:N0} Lea";

            if (m_TextGiaBan != null)
                m_TextGiaBan.text = $"Giá bán: {giaBan:N0} Lea";

            // Cập nhật sliders
            if (m_SliderGiaMua != null)
                m_SliderGiaMua.value = giaMua;

            if (m_SliderGiaBan != null)
                m_SliderGiaBan.value = giaBan;

            // Cập nhật input fields
            if (m_InputGiaMua != null)
                m_InputGiaMua.text = giaMua.ToString();

            if (m_InputGiaBan != null)
                m_InputGiaBan.text = giaBan.ToString();
        }

        public void CapNhatSoLuong(int soLuong, int giaMua, int giaBan)
        {
            if (m_TextSoLuong != null)
                m_TextSoLuong.text = soLuong.ToString();

            if (m_SliderSoLuong != null)
                m_SliderSoLuong.value = soLuong;

            // Cập nhật tổng tiền
            CapNhatTongTien(soLuong, giaMua, giaBan);
        }

        public void CapNhatTongTien(int soLuong, int giaMua, int giaBan)
        {
            if (m_TextTongTien != null)
            {
                int tongMua = giaMua * soLuong;
                int tongBan = giaBan * soLuong;
                m_TextTongTien.text = $"Mua: {tongMua:N0} Lea | Bán: {tongBan:N0} Lea";
            }
        }

        private void CapNhatTienHienTai()
        {
            if (m_TextTienHienTai != null && m_EconomySystem != null)
            {
                int tienHienTai = m_EconomySystem.CurrencyManager.SoTienHienTai;
                m_TextTienHienTai.text = $"Tiền hiện tại: {tienHienTai:N0} Lea";
                
                // Đổi màu text dựa trên số tiền
                Color mauText = tienHienTai > 0 ? m_MauTextDuTien : m_MauTextKhongDuTien;
                m_TextTienHienTai.color = mauText;
            }
        }

        public void CapNhatButtonStates(bool coTheMua, bool coTheBan, bool choPhepChinhGia)
        {
            // Button mua
            if (m_ButtonMua != null)
            {
                m_ButtonMua.interactable = coTheMua;
                var colors = m_ButtonMua.colors;
                colors.normalColor = coTheMua ? m_MauButtonMua : m_MauButtonKhongHoatDong;
                m_ButtonMua.colors = colors;
            }

            // Button bán
            if (m_ButtonBan != null)
            {
                m_ButtonBan.interactable = coTheBan;
                var colors = m_ButtonBan.colors;
                colors.normalColor = coTheBan ? m_MauButtonBan : m_MauButtonKhongHoatDong;
                m_ButtonBan.colors = colors;
            }

            // Button chỉnh giá
            if (m_ButtonChinhGia != null)
            {
                m_ButtonChinhGia.interactable = choPhepChinhGia;
                var colors = m_ButtonChinhGia.colors;
                colors.normalColor = choPhepChinhGia ? m_MauButtonBinhThuong : m_MauButtonKhongHoatDong;
                m_ButtonChinhGia.colors = colors;
            }
        }
        #endregion

        #region Animation Methods
        private IEnumerator AnimationFadeIn()
        {
            float thoiGian = 0f;
            
            if (m_MainCanvasGroup != null)
                m_MainCanvasGroup.alpha = 0f;

            while (thoiGian < m_ThoiGianFadeIn)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / m_ThoiGianFadeIn;
                float curveValue = m_CurveFadeIn.Evaluate(t);

                if (m_MainCanvasGroup != null)
                    m_MainCanvasGroup.alpha = curveValue;

                if (m_CoHieuUngScale)
                {
                    float scale = Mathf.Lerp(0.8f, 1f, curveValue);
                    transform.localScale = m_ScaleGoc * scale;
                }

                yield return null;
            }

            if (m_MainCanvasGroup != null)
                m_MainCanvasGroup.alpha = 1f;

            transform.localScale = m_ScaleGoc;
            m_CoroutineAnimation = null;
        }

        private IEnumerator AnimationFadeOut()
        {
            float thoiGian = 0f;
            
            if (m_MainCanvasGroup != null)
                m_MainCanvasGroup.alpha = 1f;

            while (thoiGian < m_ThoiGianFadeOut)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / m_ThoiGianFadeOut;
                float curveValue = m_CurveFadeOut.Evaluate(t);

                if (m_MainCanvasGroup != null)
                    m_MainCanvasGroup.alpha = curveValue;

                if (m_CoHieuUngScale)
                {
                    float scale = Mathf.Lerp(1f, 0.8f, 1f - curveValue);
                    transform.localScale = m_ScaleGoc * scale;
                }

                yield return null;
            }

            if (m_MainCanvasGroup != null)
                m_MainCanvasGroup.alpha = 0f;

            if (m_MainCanvas != null)
                m_MainCanvas.gameObject.SetActive(false);

            transform.localScale = m_ScaleGoc;
            m_UIVisible = false;
            m_CoroutineAnimation = null;
        }

        public void AnimationButtonClick(Button button)
        {
            if (button != null && m_CoHieuUngScale)
            {
                StartCoroutine(AnimationButtonScale(button.transform));
            }
        }

        private IEnumerator AnimationButtonScale(Transform buttonTransform)
        {
            Vector3 scaleGoc = buttonTransform.localScale;
            Vector3 scaleMuc = scaleGoc * m_ScaleHieuUng;
            
            float thoiGian = 0f;
            
            // Scale lên
            while (thoiGian < m_ThoiGianScale / 2)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / (m_ThoiGianScale / 2);
                buttonTransform.localScale = Vector3.Lerp(scaleGoc, scaleMuc, t);
                yield return null;
            }
            
            thoiGian = 0f;
            
            // Scale xuống
            while (thoiGian < m_ThoiGianScale / 2)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / (m_ThoiGianScale / 2);
                buttonTransform.localScale = Vector3.Lerp(scaleMuc, scaleGoc, t);
                yield return null;
            }
            
            buttonTransform.localScale = scaleGoc;
        }
        #endregion

        #region Event Handlers
        private void OnTienChanged(int tienMoi)
        {
            CapNhatTienHienTai();
            Log($"Tiền thay đổi: {tienMoi}");
        }

        private void OnInventoryChanged()
        {
            // Cập nhật button states khi inventory thay đổi
            Log("Inventory thay đổi");
        }

        private void OnItemTraded(TradingMachine machine, string itemId, int quantity, int price)
        {
            if (machine == m_TradingMachine)
            {
                Log($"Giao dịch: {quantity} {itemId} với giá {price}");
                
                // Có thể thêm animation hoặc effect ở đây
                if (quantity > 0)
                {
                    // Mua
                    AnimationButtonClick(m_ButtonMua);
                }
                else
                {
                    // Bán
                    AnimationButtonClick(m_ButtonBan);
                }
            }
        }

        private void OnPriceChanged(TradingMachine machine, int giaMua, int giaBan)
        {
            if (machine == m_TradingMachine)
            {
                CapNhatGia(giaMua, giaBan);
                Log($"Giá thay đổi - Mua: {giaMua}, Bán: {giaBan}");
            }
        }
        #endregion

        #region Public Methods
        public void SetTradingMachine(TradingMachine tradingMachine)
        {
            m_TradingMachine = tradingMachine;
        }

        public void PlayButtonClickAnimation(string buttonName)
        {
            Button button = null;
            
            switch (buttonName.ToLower())
            {
                case "mua":
                    button = m_ButtonMua;
                    break;
                case "ban":
                    button = m_ButtonBan;
                    break;
                case "dong":
                    button = m_ButtonDong;
                    break;
                case "chinhgia":
                    button = m_ButtonChinhGia;
                    break;
            }
            
            if (button != null)
                AnimationButtonClick(button);
        }
        #endregion

        #region Utility Methods
        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[TradingMachineUI] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_HienThiLog)
                Debug.LogError($"[TradingMachineUI] {message}");
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Test Hiển Thị UI")]
        private void TestHienThiUI()
        {
            HienThiUI();
        }

        [ContextMenu("Test Ẩn UI")]
        private void TestAnUI()
        {
            AnUI();
        }

        [ContextMenu("Test Animation Button")]
        private void TestAnimationButton()
        {
            if (m_ButtonMua != null)
                AnimationButtonClick(m_ButtonMua);
        }
        #endif
        #endregion
    }
}
