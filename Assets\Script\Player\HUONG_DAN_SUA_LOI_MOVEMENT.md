# Hướng Dẫn Sửa Lỗi Movement Direction

## Vấn Đề
- <PERSON><PERSON> <PERSON><PERSON> sang trái thì nhân vật di chuyển sang phải
- Khi ấn tiến lên thì nhân vật lùi về sau
- Hướng di chuyển bị đảo ngược
- Nhân vật không di chuyển theo hướng camera (quay camera thay đổi hướng di chuyển)

## Giải Pháp Đã Áp Dụng

### 1. Sửa PlayerController.cs
**File:** `Assets\Script\Player\PlayerController.cs`

#### Thêm Movement Settings:
```csharp
[Header("Movement Settings")]
[<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("<PERSON> chuyển theo hướng camera (true) hay theo hướng player (false)")]
private bool m_CameraRelativeMovement = true;

[Header("Input Debug")]
[Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON> ngược input X (trái/phải)")]
private bool m_InvertInputX = false;

[<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON> ngược input Y (tiến/lùi)")]
private bool m_InvertInputY = false;

[SerializeField, Tooltip("Hiển thị debug movement info")]
private bool m_ShowMovementDebug = false;
```

#### Cập Nhật CalculateMoveDirection():
- **Camera-Relative Movement**: Di chuyển theo hướng camera (mặc định)
- **Player-Relative Movement**: Di chuyển theo hướng player (cách cũ)
- Thêm logic để đảo ngược input X và Y
- Thêm debug logging để kiểm tra
- Có thể điều chỉnh qua Inspector

### 2. Thêm InputDirectionTester.cs
**File:** `Assets\Script\Player\InputDirectionTester.cs`

Script debug để:
- Hiển thị input và movement direction real-time
- Vẽ arrows trong Scene view
- Kiểm tra tính đúng đắn của hướng di chuyển

## Cách Sử Dụng

### Bước 1: Kiểm Tra Settings
1. Chọn Player GameObject trong Hierarchy
2. Trong Inspector, tìm **PlayerController** component
3. Mở section **Movement Settings**
4. Đảm bảo:
   - `Camera Relative Movement` = ✓ (checked) - **QUAN TRỌNG**
5. Mở section **Input Debug**
6. Kiểm tra:
   - `Invert Input X` = ☐ (unchecked)
   - `Invert Input Y` = ☐ (unchecked)

### Bước 2: Test Movement
1. Chạy game (Play mode)
2. Thử các phím:
   - **W** - Phải tiến lên phía trước
   - **S** - Phải lùi về phía sau  
   - **A** - Phải sang trái
   - **D** - Phải sang phải

### Bước 3: Debug (Nếu Vẫn Sai)
1. Thêm **InputDirectionTester** component vào Player GameObject
2. Bật `Show Debug` = ✓
3. Bật `Show Arrows` = ✓
4. Chạy game và xem Scene view
5. Kiểm tra:
   - **Mũi tên đỏ**: Input direction
   - **Mũi tên xanh**: Movement direction
   - **Console**: Debug info

### Bước 4: Điều Chỉnh (Nếu Cần)
Nếu hướng vẫn sai, điều chỉnh trong Inspector:

**Nếu trái/phải vẫn sai:**
- Bỏ tick `Invert Input X`

**Nếu tiến/lùi vẫn sai:**
- Bỏ tick `Invert Input Y`

**Để xem debug info:**
- Bật `Show Movement Debug` = ✓

## Kiểm Tra Kết Quả

### Hành Vi Mong Đợi:
- **W (Forward)**: Input.y > 0 → Direction.z > 0
- **S (Backward)**: Input.y < 0 → Direction.z < 0  
- **A (Left)**: Input.x < 0 → Direction.x < 0
- **D (Right)**: Input.x > 0 → Direction.x > 0

### Debug Console Output:
```
=== INPUT DIRECTION TEST ===
Raw Input: (1.00, 0.00)     // Ấn D
Move Direction: (1.00, 0.00, 0.00)  // Di chuyển sang phải ✓
Forward/Backward: ✓ CORRECT
Left/Right: ✓ CORRECT
```

## Troubleshooting

### Vấn Đề 1: Vẫn Bị Đảo Ngược
**Giải pháp:**
1. Kiểm tra Input System mapping trong `InputSystem_Actions.inputactions`
2. Thử đổi `Invert Input X/Y` settings
3. Kiểm tra camera rotation có ảnh hưởng không

### Vấn Đề 2: Movement Không Mượt
**Giải pháp:**
1. Kiểm tra `Acceleration` và `Deceleration` trong PlayerController
2. Điều chỉnh `Air Control` nếu cần
3. Kiểm tra frame rate

### Vấn Đề 3: Debug Arrows Không Hiện
**Giải pháp:**
1. Đảm bảo đang ở Play mode
2. Mở Scene view (không phải Game view)
3. Kiểm tra `Show Arrows` = ✓ trong InputDirectionTester

## Lưu Ý
- Các thay đổi chỉ có hiệu lực trong Play mode
- Debug options có thể được điều chỉnh real-time trong Inspector
- InputDirectionTester chỉ dùng để debug, có thể xóa sau khi sửa xong
