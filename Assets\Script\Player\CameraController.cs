using UnityEngine;
using UnityEngine.InputSystem;

namespace PlayerSystem
{
    /// <summary>
    /// H<PERSON> thống điều khiển camera hỗ trợ First Person và Third Person
    /// Tích hợp với PlayerController và Input System
    /// </summary>
    public class CameraController : MonoBehaviour
    {
        #region Constants
        private const float c_MinVerticalAngle = -90f;
        private const float c_MaxVerticalAngle = 90f;
        private const float c_CameraCollisionBuffer = 0.2f;
        #endregion

        #region Private Fields
        [Header("Camera References")]
        [SerializeField, Toolt<PERSON>("Camera chính của player")]
        private Camera m_PlayerCamera;
        
        [SerializeField, Tooltip("Transform để camera theo dõi (thường là đầu player)")]
        private Transform m_CameraTarget;

        [Header("Settings Reference")]
        [SerializeField, Tooltip("Reference đến PlayerSettings ScriptableObject")]
        private PlayerSettings m_PlayerSettings;

        [Header("Mouse Look Settings")]
        [SerializeField, Range(50f, 500f), <PERSON><PERSON><PERSON>("Độ nhạy chuột")]
        private float m_MouseSensitivity = 100f;

        [SerializeField, Range(50f, 500f), Tooltip("Độ nhạy gamepad")]
        private float m_GamepadSensitivity = 150f;

        [SerializeField, Tooltip("Đảo trục Y")]
        private bool m_InvertY = false;

        [SerializeField, Tooltip("Camera mượt mà")]
        private bool m_SmoothCamera = true;

        [SerializeField, Range(0.01f, 0.5f), Tooltip("Thời gian làm mượt camera")]
        private float m_SmoothTime = 0.1f;

        [Header("Third Person Settings")]
        [SerializeField, Range(2f, 10f), Tooltip("Khoảng cách camera trong Third Person")]
        private float m_ThirdPersonDistance = 5f;
        
        [SerializeField, Tooltip("Offset camera trong Third Person")]
        private Vector3 m_ThirdPersonOffset = new Vector3(0, 2f, 0);
        
        [SerializeField, Range(1f, 10f), Tooltip("Tốc độ chuyển đổi camera")]
        private float m_CameraTransitionSpeed = 5f;
        
        [SerializeField, Tooltip("Layer mask cho va chạm camera")]
        private LayerMask m_CameraCollisionLayers = 1;

        [Header("First Person Settings")]
        [SerializeField, Tooltip("Offset camera trong First Person")]
        private Vector3 m_FirstPersonOffset = new Vector3(0, 0.6f, 0);

        // Private camera variables
        private Vector2 m_LookInput;
        private Vector2 m_CurrentRotation;
        private Vector2 m_TargetRotation;
        private Vector2 m_RotationVelocity;
        private bool m_IsFirstPerson = true;
        private Vector3 m_CurrentCameraPosition;
        private Vector3 m_TargetCameraPosition;
        private Vector3 m_CameraVelocity;
        private PlayerController m_PlayerController;
        private bool m_IsTransitioning = false;
        private float m_TransitionProgress = 0f;

        // Input smoothing variables
        private Vector2 m_SmoothedLookInput;
        private Vector2 m_LookInputVelocity;
        private float m_LastInputTime;
        private bool m_UseHighDPI = false;
        #endregion

        #region Public Properties
        /// <summary>Kiểm tra xem có đang ở chế độ First Person không</summary>
        public bool IsFirstPerson => m_IsFirstPerson;
        
        /// <summary>Camera chính của player</summary>
        public Camera PlayerCamera => m_PlayerCamera;
        
        /// <summary>Rotation hiện tại của camera (X: vertical, Y: horizontal)</summary>
        public Vector2 CurrentRotation => m_CurrentRotation;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            SetupInitialState();
        }

        private void LateUpdate()
        {
            UpdateCameraRotation();
            UpdateCameraPosition();
            UpdateCameraTransition();
        }
        #endregion

        #region Input Callbacks
        /// <summary>Callback cho input look từ Input System</summary>
        public void OnLook(InputAction.CallbackContext _context)
        {
            m_LookInput = _context.ReadValue<Vector2>();
        }

        /// <summary>Legacy callback cho Look - để tương thích với Input System Send Messages</summary>
        public void OnLook(Vector2 _lookInput)
        {
            m_LookInput = _lookInput;
        }
        #endregion

        #region Private Methods
        private void InitializeComponents()
        {
            // Tìm PlayerController
            m_PlayerController = GetComponentInParent<PlayerController>();
            
            if (m_PlayerController == null)
            {
                Debug.LogError("CameraController: PlayerController không tìm thấy trong parent!", this);
            }
            
            // Auto-setup camera nếu chưa có
            if (m_PlayerCamera == null)
            {
                m_PlayerCamera = GetComponentInChildren<Camera>();
                if (m_PlayerCamera == null)
                {
                    Debug.LogError("CameraController: Camera component không tìm thấy!", this);
                }
            }
            
            // Auto-setup camera target
            if (m_CameraTarget == null)
            {
                m_CameraTarget = transform;
            }
        }

        private void SetupInitialState()
        {
            // Khởi tạo rotation từ transform hiện tại
            m_CurrentRotation = new Vector2(transform.eulerAngles.x, transform.eulerAngles.y);
            m_TargetRotation = m_CurrentRotation;

            // Khởi tạo input smoothing
            m_SmoothedLookInput = Vector2.zero;
            m_LookInputVelocity = Vector2.zero;
            m_LastInputTime = Time.unscaledTime;

            // Detect high DPI
            DetectHighDPI();

            // Load settings from PlayerSettings if available
            LoadSettingsFromPlayerSettings();

            // Setup camera position
            UpdateCameraMode();

            // Đăng ký với PlayerController
            m_PlayerController?.SetCameraController(this);
        }

        private void UpdateCameraRotation()
        {
            // Process input smoothing first
            ProcessInputSmoothing();

            Vector2 processedInput = GetProcessedLookInput();
            if (processedInput.magnitude < 0.01f) return;

            // Tính toán sensitivity dựa trên input device
            float sensitivity = GetCurrentSensitivity();

            // Apply mouse acceleration if enabled
            float acceleration = GetMouseAcceleration(processedInput.magnitude);

            // Áp dụng input với frame rate independence
            float deltaTime = GetFrameRateIndependentDeltaTime();
            float mouseX = processedInput.x * sensitivity * acceleration * deltaTime;
            float mouseY = processedInput.y * sensitivity * acceleration * deltaTime;

            // Đảo Y nếu cần
            if (GetInvertY())
            {
                mouseY = -mouseY;
            }

            // Cập nhật target rotation
            m_TargetRotation.y += mouseX;
            m_TargetRotation.x -= mouseY;

            // Clamp vertical rotation
            m_TargetRotation.x = Mathf.Clamp(m_TargetRotation.x, c_MinVerticalAngle, c_MaxVerticalAngle);

            // Apply smoothing based on interpolation type
            ApplyRotationSmoothing();

            // Áp dụng rotation
            ApplyCameraRotation();
        }

        private void ApplyCameraRotation()
        {
            if (m_IsFirstPerson)
            {
                // First Person: Xoay cả player và camera
                transform.rotation = Quaternion.Euler(0f, m_CurrentRotation.y, 0f);
                m_PlayerCamera.transform.localRotation = Quaternion.Euler(m_CurrentRotation.x, 0f, 0f);
            }
            else
            {
                // Third Person: Chỉ xoay player theo Y, camera theo cả X và Y
                transform.rotation = Quaternion.Euler(0f, m_CurrentRotation.y, 0f);
                
                // Camera rotation cho third person sẽ được xử lý trong UpdateCameraPosition
            }
        }

        private void UpdateCameraPosition()
        {
            if (m_PlayerCamera == null || m_CameraTarget == null) return;
            
            if (m_IsFirstPerson)
            {
                // First Person: Camera ở vị trí đầu player
                m_TargetCameraPosition = m_CameraTarget.position + m_FirstPersonOffset;
            }
            else
            {
                // Third Person: Camera ở phía sau player
                CalculateThirdPersonPosition();
            }
            
            // Smooth camera movement
            if (m_SmoothCamera)
            {
                m_CurrentCameraPosition = Vector3.SmoothDamp(m_CurrentCameraPosition, m_TargetCameraPosition, 
                    ref m_CameraVelocity, m_SmoothTime);
            }
            else
            {
                m_CurrentCameraPosition = m_TargetCameraPosition;
            }
            
            m_PlayerCamera.transform.position = m_CurrentCameraPosition;
        }

        private void CalculateThirdPersonPosition()
        {
            // Tính toán vị trí camera cho Third Person
            Vector3 targetPosition = m_CameraTarget.position + m_ThirdPersonOffset;
            Vector3 cameraDirection = Quaternion.Euler(m_CurrentRotation.x, m_CurrentRotation.y, 0f) * Vector3.back;
            
            // Vị trí mong muốn của camera
            Vector3 desiredPosition = targetPosition + cameraDirection * m_ThirdPersonDistance;
            
            // Kiểm tra va chạm
            if (Physics.Linecast(targetPosition, desiredPosition, out RaycastHit hit, m_CameraCollisionLayers))
            {
                // Điều chỉnh vị trí camera để tránh va chạm
                m_TargetCameraPosition = hit.point + hit.normal * c_CameraCollisionBuffer;
            }
            else
            {
                m_TargetCameraPosition = desiredPosition;
            }
            
            // Cập nhật camera rotation cho third person
            Vector3 lookDirection = targetPosition - m_TargetCameraPosition;
            if (lookDirection.magnitude > 0.01f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(lookDirection);
                m_PlayerCamera.transform.rotation = targetRotation;
            }
        }

        private void UpdateCameraMode()
        {
            if (m_PlayerCamera == null) return;

            // Bắt đầu transition mượt mà
            if (!m_IsTransitioning)
            {
                m_IsTransitioning = true;
                m_TransitionProgress = 0f;
            }

            if (m_IsFirstPerson)
            {
                // Setup First Person
                m_PlayerCamera.transform.localPosition = m_FirstPersonOffset;
                m_PlayerCamera.transform.localRotation = Quaternion.identity;
            }
            else
            {
                // Setup Third Person - position sẽ được tính toán trong UpdateCameraPosition
                m_CurrentCameraPosition = m_PlayerCamera.transform.position;
            }
        }

        private void UpdateCameraTransition()
        {
            if (!m_IsTransitioning) return;

            // Cập nhật transition progress
            m_TransitionProgress += Time.deltaTime * m_CameraTransitionSpeed;

            if (m_TransitionProgress >= 1f)
            {
                m_TransitionProgress = 1f;
                m_IsTransitioning = false;
            }

            // Áp dụng smooth transition cho FOV hoặc các thuộc tính khác nếu cần
            // Hiện tại chỉ đánh dấu là transition đã hoàn thành
        }

        /// <summary>Kiểm tra xem có đang sử dụng gamepad không</summary>
        private bool IsUsingGamepad()
        {
            if (m_PlayerController != null)
            {
                // Truy cập PlayerInputHandler thông qua PlayerController
                var inputHandler = m_PlayerController.GetComponent<PlayerInputHandler>();
                if (inputHandler != null)
                {
                    return inputHandler.IsUsingGamepad();
                }
            }

            // Fallback: kiểm tra trực tiếp từ Input System
            return UnityEngine.InputSystem.Gamepad.current != null &&
                   UnityEngine.InputSystem.Gamepad.current.wasUpdatedThisFrame;
        }

        /// <summary>Xử lý input smoothing</summary>
        private void ProcessInputSmoothing()
        {
            if (!GetEnableInputSmoothing())
            {
                m_SmoothedLookInput = m_LookInput;
                return;
            }

            float smoothTime = GetInputSmoothTime();
            float deltaTime = Time.unscaledDeltaTime;

            // Smooth input using SmoothDamp
            m_SmoothedLookInput.x = Mathf.SmoothDamp(m_SmoothedLookInput.x, m_LookInput.x,
                ref m_LookInputVelocity.x, smoothTime, Mathf.Infinity, deltaTime);
            m_SmoothedLookInput.y = Mathf.SmoothDamp(m_SmoothedLookInput.y, m_LookInput.y,
                ref m_LookInputVelocity.y, smoothTime, Mathf.Infinity, deltaTime);
        }

        /// <summary>Lấy processed look input</summary>
        private Vector2 GetProcessedLookInput()
        {
            return m_SmoothedLookInput;
        }

        /// <summary>Lấy sensitivity hiện tại dựa trên input device</summary>
        private float GetCurrentSensitivity()
        {
            float baseSensitivity = IsUsingGamepad() ? GetGamepadSensitivity() : GetMouseSensitivity();

            // Apply high DPI multiplier for mouse
            if (!IsUsingGamepad() && m_UseHighDPI)
            {
                baseSensitivity *= GetHighDPIMultiplier();
            }

            return baseSensitivity;
        }

        /// <summary>Tính toán mouse acceleration</summary>
        private float GetMouseAcceleration(float inputMagnitude)
        {
            if (IsUsingGamepad()) return 1f;

            float acceleration = GetMouseAcceleration();
            if (acceleration <= 1f) return 1f;

            // Apply acceleration curve
            return 1f + (acceleration - 1f) * Mathf.Pow(inputMagnitude, 0.5f);
        }

        /// <summary>Lấy delta time independent của frame rate</summary>
        private float GetFrameRateIndependentDeltaTime()
        {
            // Sử dụng unscaled delta time để tránh bị ảnh hưởng bởi time scale
            float deltaTime = Time.unscaledDeltaTime;

            // Clamp để tránh spike khi frame rate thấp
            return Mathf.Min(deltaTime, 0.033f); // Max 30 FPS equivalent
        }

        /// <summary>Áp dụng rotation smoothing dựa trên interpolation type</summary>
        private void ApplyRotationSmoothing()
        {
            if (!GetSmoothCamera())
            {
                m_CurrentRotation = m_TargetRotation;
                return;
            }

            CameraInterpolationType interpolationType = GetInterpolationType();
            float deltaTime = Time.unscaledDeltaTime;

            switch (interpolationType)
            {
                case CameraInterpolationType.None:
                    m_CurrentRotation = m_TargetRotation;
                    break;

                case CameraInterpolationType.SmoothDamp:
                    m_CurrentRotation.x = Mathf.SmoothDampAngle(m_CurrentRotation.x, m_TargetRotation.x,
                        ref m_RotationVelocity.x, GetSmoothTime(), Mathf.Infinity, deltaTime);
                    m_CurrentRotation.y = Mathf.SmoothDampAngle(m_CurrentRotation.y, m_TargetRotation.y,
                        ref m_RotationVelocity.y, GetSmoothTime(), Mathf.Infinity, deltaTime);
                    break;

                case CameraInterpolationType.Lerp:
                    float lerpSpeed = GetInterpolationSpeed();
                    m_CurrentRotation.x = Mathf.LerpAngle(m_CurrentRotation.x, m_TargetRotation.x, lerpSpeed);
                    m_CurrentRotation.y = Mathf.LerpAngle(m_CurrentRotation.y, m_TargetRotation.y, lerpSpeed);
                    break;

                case CameraInterpolationType.Slerp:
                    float slerpSpeed = GetInterpolationSpeed();
                    Quaternion currentQuat = Quaternion.Euler(m_CurrentRotation.x, m_CurrentRotation.y, 0f);
                    Quaternion targetQuat = Quaternion.Euler(m_TargetRotation.x, m_TargetRotation.y, 0f);
                    Quaternion slerpedQuat = Quaternion.Slerp(currentQuat, targetQuat, slerpSpeed);
                    Vector3 eulerAngles = slerpedQuat.eulerAngles;
                    m_CurrentRotation = new Vector2(eulerAngles.x, eulerAngles.y);
                    break;
            }
        }

        /// <summary>Detect high DPI display</summary>
        private void DetectHighDPI()
        {
            // Detect if using high DPI display (> 1.5x scaling)
            m_UseHighDPI = Screen.dpi > 150f;
        }

        /// <summary>Load settings from PlayerSettings ScriptableObject</summary>
        private void LoadSettingsFromPlayerSettings()
        {
            if (m_PlayerSettings == null) return;

            m_MouseSensitivity = m_PlayerSettings.MouseSensitivity;
            m_GamepadSensitivity = m_PlayerSettings.GamepadSensitivity;
            m_InvertY = m_PlayerSettings.InvertY;
            m_SmoothCamera = m_PlayerSettings.SmoothCamera;
            m_SmoothTime = m_PlayerSettings.SmoothTime;
            m_ThirdPersonDistance = m_PlayerSettings.ThirdPersonDistance;
            m_ThirdPersonOffset = m_PlayerSettings.ThirdPersonOffset;
            m_FirstPersonOffset = m_PlayerSettings.FirstPersonOffset;
            m_CameraTransitionSpeed = m_PlayerSettings.CameraTransitionSpeed;
        }

        // Settings getters - use PlayerSettings if available, otherwise use local values
        private float GetMouseSensitivity() => m_PlayerSettings?.MouseSensitivity ?? m_MouseSensitivity;
        private float GetGamepadSensitivity() => m_PlayerSettings?.GamepadSensitivity ?? m_GamepadSensitivity;
        private bool GetInvertY() => m_PlayerSettings?.InvertY ?? m_InvertY;
        private bool GetSmoothCamera() => m_PlayerSettings?.SmoothCamera ?? m_SmoothCamera;
        private float GetSmoothTime() => m_PlayerSettings?.SmoothTime ?? m_SmoothTime;
        private CameraInterpolationType GetInterpolationType() => m_PlayerSettings?.InterpolationType ?? CameraInterpolationType.SmoothDamp;
        private float GetInterpolationSpeed() => m_PlayerSettings?.InterpolationSpeed ?? 0.15f;
        private bool GetEnableInputSmoothing() => m_PlayerSettings?.EnableInputSmoothing ?? true;
        private float GetInputSmoothTime() => m_PlayerSettings?.InputSmoothTime ?? 0.05f;
        private float GetMouseAcceleration() => m_PlayerSettings?.MouseAcceleration ?? 1f;
        private float GetHighDPIMultiplier() => m_PlayerSettings?.HighDPIMultiplier ?? 1f;
        #endregion

        #region Public Methods
        /// <summary>Chuyển đổi giữa First Person và Third Person</summary>
        public void ToggleCameraMode()
        {
            m_IsFirstPerson = !m_IsFirstPerson;
            UpdateCameraMode();

            Debug.Log($"CameraController: Chuyển sang {(m_IsFirstPerson ? "First Person" : "Third Person")} mode");
        }

        /// <summary>Thiết lập chế độ camera cụ thể</summary>
        public void SetCameraMode(bool _isFirstPerson)
        {
            if (m_IsFirstPerson != _isFirstPerson)
            {
                m_IsFirstPerson = _isFirstPerson;
                UpdateCameraMode();
            }
        }

        /// <summary>Thiết lập độ nhạy chuột</summary>
        public void SetMouseSensitivity(float _sensitivity)
        {
            m_MouseSensitivity = Mathf.Clamp(_sensitivity, 50f, 500f);
        }

        /// <summary>Thiết lập khoảng cách Third Person</summary>
        public void SetThirdPersonDistance(float _distance)
        {
            m_ThirdPersonDistance = Mathf.Clamp(_distance, 2f, 10f);
        }

        /// <summary>Lấy hướng forward của camera</summary>
        public Vector3 GetCameraForward()
        {
            return m_PlayerCamera != null ? m_PlayerCamera.transform.forward : transform.forward;
        }

        /// <summary>Lấy hướng right của camera</summary>
        public Vector3 GetCameraRight()
        {
            return m_PlayerCamera != null ? m_PlayerCamera.transform.right : transform.right;
        }

        /// <summary>Cập nhật PlayerSettings reference</summary>
        public void SetPlayerSettings(PlayerSettings _playerSettings)
        {
            m_PlayerSettings = _playerSettings;
            LoadSettingsFromPlayerSettings();
        }

        /// <summary>Refresh settings từ PlayerSettings</summary>
        public void RefreshSettings()
        {
            LoadSettingsFromPlayerSettings();
            DetectHighDPI();
        }
        #endregion

        #if UNITY_EDITOR
        [ContextMenu("Switch to First Person")]
        private void SwitchToFirstPerson()
        {
            SetCameraMode(true);
        }

        [ContextMenu("Switch to Third Person")]
        private void SwitchToThirdPerson()
        {
            SetCameraMode(false);
        }

        [ContextMenu("Reset Camera Settings")]
        private void ResetCameraSettings()
        {
            m_MouseSensitivity = 100f;
            m_ThirdPersonDistance = 5f;
            m_ThirdPersonOffset = new Vector3(0, 2f, 0);
            m_FirstPersonOffset = new Vector3(0, 0.6f, 0);
            Debug.Log("CameraController: Camera settings đã được reset về mặc định.");
        }

        private void OnDrawGizmosSelected()
        {
            if (m_CameraTarget == null) return;
            
            // Vẽ third person camera position
            if (!m_IsFirstPerson)
            {
                Gizmos.color = Color.yellow;
                Vector3 targetPos = m_CameraTarget.position + m_ThirdPersonOffset;
                Gizmos.DrawWireSphere(targetPos, 0.2f);
                
                Gizmos.color = Color.cyan;
                Vector3 cameraDir = Quaternion.Euler(m_CurrentRotation.x, m_CurrentRotation.y, 0f) * Vector3.back;
                Vector3 cameraPos = targetPos + cameraDir * m_ThirdPersonDistance;
                Gizmos.DrawWireSphere(cameraPos, 0.1f);
                Gizmos.DrawLine(targetPos, cameraPos);
            }
        }
        #endif
    }
}
