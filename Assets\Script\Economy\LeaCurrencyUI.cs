using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

namespace EconomySystem
{
    /// <summary>
    /// Quản lý giao diện hiển thị tiền tệ Lea
    /// Hỗ trợ animation và visual effects
    /// </summary>
    public class LeaCurrencyUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 UI References")]
        [SerializeField] private TextMeshProUGUI m_TextTienTe;
        [SerializeField] private Image m_IconTienTe;
        [SerializeField] private Slider m_SliderTienTe;
        [SerializeField] private GameObject m_PanelTienTe;

        [Header("🎭 Animation Settings")]
        [SerializeField] private bool m_CoAnimation = true;
        [SerializeField] private float m_ThoiGianAnimation = 0.5f;
        [SerializeField] private AnimationCurve m_CurveTangTien = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private AnimationCurve m_CurveGiamTien = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("🌈 Color Settings")]
        [SerializeField] private Color m_MauBinhThuong = Color.white;
        [SerializeField] private Color m_MauTangTien = Color.green;
        [SerializeField] private Color m_MauGiamTien = Color.red;
        [SerializeField] private Color m_MauKhongDuTien = Color.red;

        [Header("✨ Effects")]
        [SerializeField] private bool m_CoHieuUngRung = true;
        [SerializeField] private float m_CuongDoRung = 5f;
        [SerializeField] private ParticleSystem m_HieuUngTangTien;
        [SerializeField] private ParticleSystem m_HieuUngGiamTien;

        [Header("🔧 Settings")]
        [SerializeField] private string m_DinhDangTien = "{0:N0} Lea";
        [SerializeField] private bool m_HienThiSlider = true;
        [SerializeField] private bool m_HienThiLog = false;
        #endregion

        #region Private Fields
        private LeaCurrencyManager m_CurrencyManager;
        private int m_TienHienTai = 0;
        private int m_TienTruoc = 0;
        private Vector3 m_ViTriGoc;
        private Coroutine m_CoroutineAnimation;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            m_ViTriGoc = transform.localPosition;
            TimCurrencyManager();
        }

        private void Start()
        {
            DangKyEvents();
            CapNhatUI();
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
        }
        #endregion

        #region Event Management
        private void DangKyEvents()
        {
            LeaCurrencyManager.OnLeaChanged += OnLeaChanged;
            LeaCurrencyManager.OnLeaTransaction += OnLeaTransaction;
            LeaCurrencyManager.OnTransactionFailed += OnTransactionFailed;
        }

        private void HuyDangKyEvents()
        {
            LeaCurrencyManager.OnLeaChanged -= OnLeaChanged;
            LeaCurrencyManager.OnLeaTransaction -= OnLeaTransaction;
            LeaCurrencyManager.OnTransactionFailed -= OnTransactionFailed;
        }
        #endregion

        #region Event Handlers
        private void OnLeaChanged(int tienMoi)
        {
            m_TienTruoc = m_TienHienTai;
            m_TienHienTai = tienMoi;
            CapNhatUI();
        }

        private void OnLeaTransaction(int soTienThayDoi, int tongTienMoi)
        {
            if (m_CoAnimation)
            {
                if (soTienThayDoi > 0)
                    AnimationTangTien();
                else
                    AnimationGiamTien();
            }

            // Hiệu ứng particle
            if (soTienThayDoi > 0 && m_HieuUngTangTien != null)
                m_HieuUngTangTien.Play();
            else if (soTienThayDoi < 0 && m_HieuUngGiamTien != null)
                m_HieuUngGiamTien.Play();

            Log($"Giao dịch: {soTienThayDoi:+#;-#;0} Lea, Tổng: {tongTienMoi}");
        }

        private void OnTransactionFailed(string lyDo)
        {
            AnimationLoi();
            Log($"Giao dịch thất bại: {lyDo}");
        }
        #endregion

        #region UI Updates
        private void CapNhatUI()
        {
            if (m_CurrencyManager == null)
                TimCurrencyManager();

            if (m_CurrencyManager != null)
            {
                // Cập nhật text
                if (m_TextTienTe != null)
                    m_TextTienTe.text = string.Format(m_DinhDangTien, m_TienHienTai);

                // Cập nhật slider
                if (m_SliderTienTe != null && m_HienThiSlider)
                {
                    m_SliderTienTe.gameObject.SetActive(true);
                    m_SliderTienTe.value = m_CurrencyManager.PhanTramTien;
                }
                else if (m_SliderTienTe != null)
                {
                    m_SliderTienTe.gameObject.SetActive(false);
                }

                // Cập nhật màu sắc
                CapNhatMauSac();
            }
        }

        private void CapNhatMauSac()
        {
            Color mauMoi = m_MauBinhThuong;

            if (m_TienHienTai > m_TienTruoc)
                mauMoi = m_MauTangTien;
            else if (m_TienHienTai < m_TienTruoc)
                mauMoi = m_MauGiamTien;

            if (m_TextTienTe != null)
                m_TextTienTe.color = mauMoi;

            if (m_IconTienTe != null)
                m_IconTienTe.color = mauMoi;

            // Trở về màu bình thường sau 1 giây
            if (mauMoi != m_MauBinhThuong)
                StartCoroutine(TroVeMauBinhThuong());
        }

        private IEnumerator TroVeMauBinhThuong()
        {
            yield return new WaitForSeconds(1f);

            if (m_TextTienTe != null)
                m_TextTienTe.color = m_MauBinhThuong;

            if (m_IconTienTe != null)
                m_IconTienTe.color = m_MauBinhThuong;
        }
        #endregion

        #region Animations
        private void AnimationTangTien()
        {
            if (m_CoroutineAnimation != null)
                StopCoroutine(m_CoroutineAnimation);

            m_CoroutineAnimation = StartCoroutine(AnimationScale(1.2f, m_CurveTangTien));
        }

        private void AnimationGiamTien()
        {
            if (m_CoroutineAnimation != null)
                StopCoroutine(m_CoroutineAnimation);

            m_CoroutineAnimation = StartCoroutine(AnimationScale(0.8f, m_CurveGiamTien));
        }

        private void AnimationLoi()
        {
            if (m_CoHieuUngRung)
                StartCoroutine(HieuUngRung());

            // Đổi màu thành đỏ tạm thời
            if (m_TextTienTe != null)
                m_TextTienTe.color = m_MauKhongDuTien;

            StartCoroutine(TroVeMauBinhThuong());
        }

        private IEnumerator AnimationScale(float targetScale, AnimationCurve curve)
        {
            Vector3 scaleGoc = Vector3.one;
            Vector3 scaleMuc = Vector3.one * targetScale;
            float thoiGian = 0f;

            // Scale lên/xuống
            while (thoiGian < m_ThoiGianAnimation)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / m_ThoiGianAnimation;
                float curveValue = curve.Evaluate(t);
                
                transform.localScale = Vector3.Lerp(scaleGoc, scaleMuc, curveValue);
                yield return null;
            }

            // Scale về bình thường
            thoiGian = 0f;
            while (thoiGian < m_ThoiGianAnimation)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / m_ThoiGianAnimation;
                
                transform.localScale = Vector3.Lerp(scaleMuc, scaleGoc, t);
                yield return null;
            }

            transform.localScale = scaleGoc;
            m_CoroutineAnimation = null;
        }

        private IEnumerator HieuUngRung()
        {
            float thoiGian = 0f;
            float thoiGianRung = 0.3f;

            while (thoiGian < thoiGianRung)
            {
                thoiGian += Time.deltaTime;
                
                Vector3 viTriRung = m_ViTriGoc + Random.insideUnitSphere * m_CuongDoRung;
                transform.localPosition = viTriRung;
                
                yield return null;
            }

            transform.localPosition = m_ViTriGoc;
        }
        #endregion

        #region Public Methods
        public void HienThiPanel(bool hienThi)
        {
            if (m_PanelTienTe != null)
                m_PanelTienTe.SetActive(hienThi);
        }

        public void CapNhatDinhDangTien(string dinhDangMoi)
        {
            m_DinhDangTien = dinhDangMoi;
            CapNhatUI();
        }
        #endregion

        #region Utility Methods
        private void TimCurrencyManager()
        {
            if (m_CurrencyManager == null)
                m_CurrencyManager = FindObjectOfType<LeaCurrencyManager>();

            if (m_CurrencyManager != null)
                m_TienHienTai = m_CurrencyManager.SoTienHienTai;
        }

        private void Log(string message)
        {
            if (m_HienThiLog)
                Debug.Log($"[LeaCurrencyUI] {message}");
        }
        #endregion

        #region Context Menu (Editor Only)
        #if UNITY_EDITOR
        [ContextMenu("Test Animation Tăng Tiền")]
        private void TestAnimationTangTien()
        {
            AnimationTangTien();
        }

        [ContextMenu("Test Animation Giảm Tiền")]
        private void TestAnimationGiamTien()
        {
            AnimationGiamTien();
        }

        [ContextMenu("Test Animation Lỗi")]
        private void TestAnimationLoi()
        {
            AnimationLoi();
        }
        #endif
        #endregion
    }
}
