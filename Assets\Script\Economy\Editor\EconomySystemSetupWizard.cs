using UnityEngine;
using UnityEditor;
using TMPro;
using UnityEngine.UI;

namespace EconomySystem.Editor
{
    /// <summary>
    /// Wizard để setup nhanh hệ thống Economy
    /// </summary>
    public class EconomySystemSetupWizard : EditorWindow
    {
        #region Private Fields
        private Vector2 m_ScrollPosition;
        private bool m_TaoEconomyManager = true;
        private bool m_TaoCurrencyUI = true;
        private bool m_TaoInventoryUI = true;
        private bool m_TaoCanvas = true;
        private bool m_ThemVaoPlayer = false;
        
        private GameObject m_PlayerObject;
        private Canvas m_CanvasHienTai;
        #endregion

        #region Menu Item
        [MenuItem("Economy System/Setup Wizard", priority = 1)]
        public static void ShowWindow()
        {
            EconomySystemSetupWizard window = GetWindow<EconomySystemSetupWizard>("Economy Setup Wizard");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }
        #endregion

        #region GUI
        private void OnGUI()
        {
            m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);
            
            DrawHeader();
            DrawSetupOptions();
            DrawReferences();
            DrawButtons();
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.Space(10);
            
            GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
            titleStyle.fontSize = 18;
            titleStyle.alignment = TextAnchor.MiddleCenter;
            
            EditorGUILayout.LabelField("💰 Economy System Setup Wizard", titleStyle);
            EditorGUILayout.LabelField("Thiết lập nhanh hệ thống tiền tệ Lea & Kho hàng", EditorStyles.centeredGreyMiniLabel);
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        }

        private void DrawSetupOptions()
        {
            EditorGUILayout.LabelField("🔧 Tùy Chọn Thiết Lập", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TaoEconomyManager = EditorGUILayout.Toggle("Tạo Economy System Manager", m_TaoEconomyManager);
            m_TaoCurrencyUI = EditorGUILayout.Toggle("Tạo Currency UI", m_TaoCurrencyUI);
            m_TaoInventoryUI = EditorGUILayout.Toggle("Tạo Inventory UI", m_TaoInventoryUI);
            m_TaoCanvas = EditorGUILayout.Toggle("Tạo Canvas mới", m_TaoCanvas);
            m_ThemVaoPlayer = EditorGUILayout.Toggle("Thêm vào Player Object", m_ThemVaoPlayer);
            
            EditorGUILayout.Space(10);
        }

        private void DrawReferences()
        {
            EditorGUILayout.LabelField("🎯 References", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            if (m_ThemVaoPlayer)
            {
                m_PlayerObject = (GameObject)EditorGUILayout.ObjectField(
                    "Player Object", m_PlayerObject, typeof(GameObject), true);
            }
            
            if (!m_TaoCanvas)
            {
                m_CanvasHienTai = (Canvas)EditorGUILayout.ObjectField(
                    "Canvas Hiện Tại", m_CanvasHienTai, typeof(Canvas), true);
            }
            
            EditorGUILayout.Space(10);
        }

        private void DrawButtons()
        {
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
            EditorGUILayout.Space(10);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🚀 Thiết Lập Tự Động", GUILayout.Height(40)))
            {
                ThietLapTuDong();
            }
            
            if (GUILayout.Button("📖 Mở Hướng Dẫn", GUILayout.Height(40)))
            {
                MoHuongDan();
            }
            
            GUILayout.EndHorizontal();
            
            EditorGUILayout.Space(10);
            
            if (GUILayout.Button("🔍 Kiểm Tra Hệ Thống Hiện Tại", GUILayout.Height(30)))
            {
                KiemTraHeThongHienTai();
            }
        }
        #endregion

        #region Setup Methods
        private void ThietLapTuDong()
        {
            try
            {
                EditorUtility.DisplayProgressBar("Economy Setup", "Đang thiết lập hệ thống...", 0f);
                
                GameObject economySystem = null;
                Canvas canvas = null;
                
                // Tạo Economy System Manager
                if (m_TaoEconomyManager)
                {
                    EditorUtility.DisplayProgressBar("Economy Setup", "Tạo Economy System Manager...", 0.2f);
                    economySystem = TaoEconomySystemManager();
                }
                
                // Tạo Canvas
                if (m_TaoCanvas)
                {
                    EditorUtility.DisplayProgressBar("Economy Setup", "Tạo Canvas...", 0.4f);
                    canvas = TaoCanvas();
                }
                else
                {
                    canvas = m_CanvasHienTai;
                }
                
                // Tạo Currency UI
                if (m_TaoCurrencyUI && canvas != null)
                {
                    EditorUtility.DisplayProgressBar("Economy Setup", "Tạo Currency UI...", 0.6f);
                    TaoCurrencyUI(canvas, economySystem);
                }
                
                // Tạo Inventory UI
                if (m_TaoInventoryUI && canvas != null)
                {
                    EditorUtility.DisplayProgressBar("Economy Setup", "Tạo Inventory UI...", 0.8f);
                    TaoInventoryUI(canvas, economySystem);
                }
                
                // Thêm vào Player
                if (m_ThemVaoPlayer && m_PlayerObject != null && economySystem != null)
                {
                    EditorUtility.DisplayProgressBar("Economy Setup", "Tích hợp với Player...", 0.9f);
                    TichHopVoiPlayer();
                }
                
                EditorUtility.DisplayProgressBar("Economy Setup", "Hoàn thành!", 1f);
                
                EditorUtility.DisplayDialog("Thành Công!", 
                    "Đã thiết lập xong hệ thống Economy!\n\n" +
                    "Hãy kiểm tra:\n" +
                    "- EconomySystem GameObject\n" +
                    "- Canvas với UI components\n" +
                    "- Console logs để xem trạng thái\n\n" +
                    "Sử dụng Context Menu để test các chức năng!", 
                    "OK");
            }
            catch (System.Exception e)
            {
                EditorUtility.DisplayDialog("Lỗi!", $"Có lỗi xảy ra: {e.Message}", "OK");
                Debug.LogError($"[EconomySetupWizard] Lỗi: {e.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private GameObject TaoEconomySystemManager()
        {
            GameObject economySystem = new GameObject("EconomySystem");
            
            // Thêm các components
            economySystem.AddComponent<EconomySystemManager>();
            economySystem.AddComponent<LeaCurrencyManager>();
            economySystem.AddComponent<PlayerInventory>();
            
            // Đặt vị trí
            economySystem.transform.position = Vector3.zero;
            
            Debug.Log("[EconomySetupWizard] Đã tạo EconomySystem GameObject");
            return economySystem;
        }

        private Canvas TaoCanvas()
        {
            GameObject canvasObj = new GameObject("EconomyCanvas");
            Canvas canvas = canvasObj.AddComponent<Canvas>();
            canvasObj.AddComponent<CanvasScaler>();
            canvasObj.AddComponent<GraphicRaycaster>();
            
            // Cấu hình Canvas
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 100;
            
            // Cấu hình CanvasScaler
            CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;
            
            Debug.Log("[EconomySetupWizard] Đã tạo Canvas");
            return canvas;
        }

        private void TaoCurrencyUI(Canvas canvas, GameObject economySystem)
        {
            // Tạo Currency HUD
            GameObject currencyHUD = new GameObject("CurrencyHUD");
            currencyHUD.transform.SetParent(canvas.transform, false);
            
            // Thêm RectTransform và cấu hình
            RectTransform rectTransform = currencyHUD.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(1, 1);
            rectTransform.anchorMax = new Vector2(1, 1);
            rectTransform.pivot = new Vector2(1, 1);
            rectTransform.anchoredPosition = new Vector2(-20, -20);
            rectTransform.sizeDelta = new Vector2(200, 60);
            
            // Thêm Background
            Image background = currencyHUD.AddComponent<Image>();
            background.color = new Color(0, 0, 0, 0.5f);
            
            // Tạo Currency Text
            GameObject textObj = new GameObject("CurrencyText");
            textObj.transform.SetParent(currencyHUD.transform, false);
            
            TextMeshProUGUI currencyText = textObj.AddComponent<TextMeshProUGUI>();
            currencyText.text = "100 Lea";
            currencyText.fontSize = 24;
            currencyText.color = Color.white;
            currencyText.alignment = TextAlignmentOptions.Center;
            
            RectTransform textRect = textObj.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            // Thêm LeaCurrencyUI component
            LeaCurrencyUI currencyUI = currencyHUD.AddComponent<LeaCurrencyUI>();
            
            // Gán reference vào EconomySystemManager
            if (economySystem != null)
            {
                EconomySystemManager manager = economySystem.GetComponent<EconomySystemManager>();
                if (manager != null)
                {
                    // Sử dụng SerializedObject để gán reference
                    SerializedObject so = new SerializedObject(manager);
                    so.FindProperty("m_CurrencyUI").objectReferenceValue = currencyUI;
                    so.ApplyModifiedProperties();
                }
            }
            
            Debug.Log("[EconomySetupWizard] Đã tạo Currency UI");
        }

        private void TaoInventoryUI(Canvas canvas, GameObject economySystem)
        {
            // Tạo Inventory Panel
            GameObject inventoryPanel = new GameObject("InventoryPanel");
            inventoryPanel.transform.SetParent(canvas.transform, false);
            inventoryPanel.SetActive(false); // Ẩn ban đầu
            
            // Cấu hình RectTransform
            RectTransform rectTransform = inventoryPanel.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            
            // Thêm Background
            Image background = inventoryPanel.AddComponent<Image>();
            background.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);
            
            // Tạo Title
            GameObject titleObj = new GameObject("Title");
            titleObj.transform.SetParent(inventoryPanel.transform, false);
            
            TextMeshProUGUI titleText = titleObj.AddComponent<TextMeshProUGUI>();
            titleText.text = "Kho Hàng";
            titleText.fontSize = 32;
            titleText.color = Color.white;
            titleText.alignment = TextAlignmentOptions.Center;
            
            RectTransform titleRect = titleObj.GetComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 1);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.pivot = new Vector2(0.5f, 1);
            titleRect.anchoredPosition = new Vector2(0, -20);
            titleRect.sizeDelta = new Vector2(0, 50);
            
            // Thêm InventoryUI component
            InventoryUI inventoryUI = inventoryPanel.AddComponent<InventoryUI>();
            
            // Gán reference vào EconomySystemManager
            if (economySystem != null)
            {
                EconomySystemManager manager = economySystem.GetComponent<EconomySystemManager>();
                if (manager != null)
                {
                    SerializedObject so = new SerializedObject(manager);
                    so.FindProperty("m_InventoryUI").objectReferenceValue = inventoryUI;
                    so.ApplyModifiedProperties();
                }
            }
            
            Debug.Log("[EconomySetupWizard] Đã tạo Inventory UI");
        }

        private void TichHopVoiPlayer()
        {
            // Logic tích hợp với Player object
            Debug.Log("[EconomySetupWizard] Đã tích hợp với Player");
        }

        private void MoHuongDan()
        {
            string path = "Assets/Script/Economy/HUONG_DAN_CAI_DAT_LEA_CURRENCY_SYSTEM.md";
            UnityEditorInternal.InternalEditorUtility.OpenFileAtLineExternal(path, 1);
        }

        private void KiemTraHeThongHienTai()
        {
            string report = "=== KIỂM TRA HỆ THỐNG ECONOMY ===\n\n";
            
            // Kiểm tra EconomySystemManager
            EconomySystemManager economyManager = FindObjectOfType<EconomySystemManager>();
            report += $"EconomySystemManager: {(economyManager != null ? "✓ Có" : "✗ Không có")}\n";
            
            // Kiểm tra LeaCurrencyManager
            LeaCurrencyManager currencyManager = FindObjectOfType<LeaCurrencyManager>();
            report += $"LeaCurrencyManager: {(currencyManager != null ? "✓ Có" : "✗ Không có")}\n";
            
            // Kiểm tra PlayerInventory
            PlayerInventory inventory = FindObjectOfType<PlayerInventory>();
            report += $"PlayerInventory: {(inventory != null ? "✓ Có" : "✗ Không có")}\n";
            
            // Kiểm tra UI
            LeaCurrencyUI currencyUI = FindObjectOfType<LeaCurrencyUI>();
            report += $"LeaCurrencyUI: {(currencyUI != null ? "✓ Có" : "✗ Không có")}\n";
            
            InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
            report += $"InventoryUI: {(inventoryUI != null ? "✓ Có" : "✗ Không có")}\n";
            
            // Kiểm tra Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            report += $"Canvas: {(canvas != null ? "✓ Có" : "✗ Không có")}\n";
            
            report += "\n=== KẾT LUẬN ===\n";
            if (economyManager != null && currencyManager != null && inventory != null)
            {
                report += "✅ Hệ thống Economy đã được thiết lập cơ bản!";
            }
            else
            {
                report += "❌ Hệ thống Economy chưa được thiết lập đầy đủ.";
            }
            
            EditorUtility.DisplayDialog("Báo Cáo Hệ Thống", report, "OK");
            Debug.Log($"[EconomySetupWizard]\n{report}");
        }
        #endregion
    }
}
